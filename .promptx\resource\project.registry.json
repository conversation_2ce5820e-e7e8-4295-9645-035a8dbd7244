{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T14:11:44.567Z", "updatedAt": "2025-07-29T14:11:44.615Z", "resourceCount": 24}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.572Z", "updatedAt": "2025-07-29T14:11:44.572Z", "scannedAt": "2025-07-29T14:11:44.572Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.576Z", "updatedAt": "2025-07-29T14:11:44.576Z", "scannedAt": "2025-07-29T14:11:44.576Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.579Z", "updatedAt": "2025-07-29T14:11:44.579Z", "scannedAt": "2025-07-29T14:11:44.579Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.581Z", "updatedAt": "2025-07-29T14:11:44.581Z", "scannedAt": "2025-07-29T14:11:44.581Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.583Z", "updatedAt": "2025-07-29T14:11:44.583Z", "scannedAt": "2025-07-29T14:11:44.583Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.585Z", "updatedAt": "2025-07-29T14:11:44.585Z", "scannedAt": "2025-07-29T14:11:44.585Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.586Z", "updatedAt": "2025-07-29T14:11:44.586Z", "scannedAt": "2025-07-29T14:11:44.586Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.588Z", "updatedAt": "2025-07-29T14:11:44.588Z", "scannedAt": "2025-07-29T14:11:44.588Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.589Z", "updatedAt": "2025-07-29T14:11:44.589Z", "scannedAt": "2025-07-29T14:11:44.589Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.593Z", "updatedAt": "2025-07-29T14:11:44.593Z", "scannedAt": "2025-07-29T14:11:44.593Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.595Z", "updatedAt": "2025-07-29T14:11:44.595Z", "scannedAt": "2025-07-29T14:11:44.595Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.597Z", "updatedAt": "2025-07-29T14:11:44.597Z", "scannedAt": "2025-07-29T14:11:44.597Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.599Z", "updatedAt": "2025-07-29T14:11:44.599Z", "scannedAt": "2025-07-29T14:11:44.599Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.600Z", "updatedAt": "2025-07-29T14:11:44.600Z", "scannedAt": "2025-07-29T14:11:44.600Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.601Z", "updatedAt": "2025-07-29T14:11:44.601Z", "scannedAt": "2025-07-29T14:11:44.601Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.603Z", "updatedAt": "2025-07-29T14:11:44.603Z", "scannedAt": "2025-07-29T14:11:44.603Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.604Z", "updatedAt": "2025-07-29T14:11:44.604Z", "scannedAt": "2025-07-29T14:11:44.604Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.605Z", "updatedAt": "2025-07-29T14:11:44.605Z", "scannedAt": "2025-07-29T14:11:44.605Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-29T14:11:44.606Z", "updatedAt": "2025-07-29T14:11:44.606Z", "scannedAt": "2025-07-29T14:11:44.606Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-29T14:11:44.608Z", "updatedAt": "2025-07-29T14:11:44.608Z", "scannedAt": "2025-07-29T14:11:44.608Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.611Z", "updatedAt": "2025-07-29T14:11:44.611Z", "scannedAt": "2025-07-29T14:11:44.611Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.612Z", "updatedAt": "2025-07-29T14:11:44.612Z", "scannedAt": "2025-07-29T14:11:44.612Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.613Z", "updatedAt": "2025-07-29T14:11:44.613Z", "scannedAt": "2025-07-29T14:11:44.613Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-29T14:11:44.614Z", "updatedAt": "2025-07-29T14:11:44.614Z", "scannedAt": "2025-07-29T14:11:44.614Z", "path": "role/system-director/thought/team-coordination.thought.md"}}], "stats": {"totalResources": 24, "byProtocol": {"role": 6, "execution": 9, "thought": 9}, "bySource": {"project": 24}}}