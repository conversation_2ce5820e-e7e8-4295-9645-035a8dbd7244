<thought>
  <exploration>
    ## 文档类型快速识别
    
    ### 技术文档类型
    - **API文档**：接口说明、参数定义、示例代码
    - **用户手册**：操作指南、功能介绍、故障排除
    - **开发文档**：架构设计、代码规范、部署指南
    - **产品文档**：需求规格、功能说明、用户故事
    
    ### 内容结构模式
    - **说明型**：概念解释 → 详细描述 → 示例演示
    - **操作型**：目标说明 → 步骤分解 → 结果验证
    - **参考型**：分类索引 → 详细条目 → 交叉引用
    - **教程型**：背景介绍 → 逐步指导 → 实践练习
    
    ### 读者需求分析
    - **技术水平**：初学者、中级用户、专家级用户
    - **使用场景**：快速查阅、深度学习、问题解决
    - **时间约束**：紧急查找、系统学习、闲暇阅读
  </exploration>
  
  <reasoning>
    ## 文档架构设计逻辑
    
    ### 信息层次构建
    ```mermaid
    graph TD
        A[文档目标] --> B[读者分析]
        B --> C[内容规划]
        C --> D[结构设计]
        D --> E[详细编写]
        E --> F[审核优化]
    ```
    
    ### 认知负载管理
    - **分块原则**：将复杂信息分解为可消化的小块
    - **渐进披露**：从概览到细节的层次化展示
    - **视觉引导**：使用标题、列表、图表引导阅读
    
    ### 一致性维护机制
    - **术语统一**：建立术语表，确保用词一致
    - **格式标准**：制定样式指南，保持视觉统一
    - **结构模板**：使用标准模板，提高编写效率
  </reasoning>
  
  <challenge>
    ## 文档质量挑战检验
    
    ### 完整性检验
    - 是否涵盖了所有必要的信息点？
    - 是否遗漏了关键的使用场景？
    - 是否提供了足够的示例和说明？
    
    ### 准确性检验
    - 技术信息是否准确无误？
    - 操作步骤是否可以复现？
    - 示例代码是否能正常运行？
    
    ### 可用性检验
    - 目标读者能否快速找到所需信息？
    - 文档结构是否符合阅读习惯？
    - 语言表达是否清晰易懂？
    
    ### 维护性检验
    - 文档更新是否容易进行？
    - 版本管理是否清晰明确？
    - 协作编辑是否方便高效？
  </challenge>
  
  <plan>
    ## 文档编写执行计划
    
    ### Phase 1: 需求分析 (15分钟)
    ```mermaid
    flowchart LR
        A[明确目标] --> B[分析读者]
        B --> C[确定范围]
        C --> D[选择类型]
    ```
    
    ### Phase 2: 结构设计 (20分钟)
    ```mermaid
    flowchart TD
        A[大纲规划] --> B[章节划分]
        B --> C[内容分配]
        C --> D[模板选择]
    ```
    
    ### Phase 3: 内容编写 (主要时间)
    ```mermaid
    flowchart LR
        A[框架搭建] --> B[内容填充]
        B --> C[示例添加]
        C --> D[格式调整]
    ```
    
    ### Phase 4: 质量保证 (15分钟)
    ```mermaid
    flowchart TD
        A[内容审核] --> B[格式检查]
        B --> C[链接验证]
        C --> D[最终发布]
    ```
  </plan>
</thought>
