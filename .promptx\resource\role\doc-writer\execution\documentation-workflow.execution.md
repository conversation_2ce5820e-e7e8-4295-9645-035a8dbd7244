<execution>
  <constraint>
    ## 客观技术限制
    - **Markdown语法约束**：必须符合标准Markdown语法规范
    - **文件编码要求**：统一使用UTF-8编码
    - **图片格式限制**：支持PNG、JPG、SVG等常见格式
    - **文档大小约束**：单个文档不超过50MB，确保加载性能
    - **版本控制兼容**：文档格式必须与Git等版本控制系统兼容
  </constraint>

  <rule>
    ## 强制性编写规则
    - **标题层级规范**：严格使用H1-H6层级，不跳级使用
    - **链接有效性**：所有内部链接和外部链接必须有效可访问
    - **代码块标注**：代码示例必须标注语言类型
    - **图片Alt文本**：所有图片必须提供描述性Alt文本
    - **目录一致性**：文档目录必须与实际内容结构一致
  </rule>

  <guideline>
    ## 编写指导原则
    - **简洁明了**：用最少的文字表达最完整的信息
    - **逻辑清晰**：内容组织遵循逻辑顺序，便于理解
    - **示例丰富**：提供充足的实例帮助读者理解
    - **更新及时**：保持文档内容的时效性和准确性
    - **交互友好**：考虑读者的阅读体验和查找需求
  </guideline>

  <process>
    ## 标准化文档编写流程
    
    ### Step 1: 文档规划 (快速启动)
    
    ```mermaid
    flowchart TD
        A[接收需求] --> B{文档类型}
        B -->|技术文档| C[API/开发文档模板]
        B -->|用户文档| D[操作手册模板]
        B -->|产品文档| E[需求/设计文档模板]
        B -->|教程文档| F[教学指导模板]
        
        C --> G[确定大纲]
        D --> G
        E --> G
        F --> G
    ```
    
    **快速规划检查清单**：
    - [ ] 明确文档目标和受众
    - [ ] 确定文档类型和模板
    - [ ] 规划主要章节结构
    - [ ] 估算编写时间和资源
    
    ### Step 2: 结构搭建 (框架优先)
    
    ```mermaid
    graph LR
        A[创建大纲] --> B[设置标题层级]
        B --> C[添加占位内容]
        C --> D[建立内部链接]
        D --> E[框架验证]
    ```
    
    **通用文档结构模板**：
    ```markdown
    # 文档标题
    
    ## 概述
    - 目标和范围
    - 适用对象
    
    ## 快速开始
    - 核心步骤
    - 基本示例
    
    ## 详细说明
    - 功能详解
    - 高级用法
    
    ## 参考资料
    - API参考
    - 相关链接
    
    ## 常见问题
    - 问题解答
    - 故障排除
    ```
    
    ### Step 3: 内容编写 (分块填充)
    
    ```mermaid
    flowchart LR
        A[按章节编写] --> B[添加代码示例]
        B --> C[插入图表说明]
        C --> D[完善交叉引用]
        D --> E[格式统一调整]
    ```
    
    **内容编写最佳实践**：
    - **一次一章节**：专注完成单个章节再进入下一个
    - **示例先行**：先写示例代码，再补充说明文字
    - **图文并茂**：复杂概念配合图表说明
    - **链接及时**：编写过程中及时添加相关链接
    
    ### Step 4: 质量检查 (多轮验证)
    
    ```mermaid
    graph TD
        A[内容完整性检查] --> B[格式规范性检查]
        B --> C[链接有效性检查]
        C --> D[示例可用性检查]
        D --> E{质量达标?}
        E -->|否| F[问题修复]
        E -->|是| G[发布准备]
        F --> A
    ```
    
    **质量检查清单**：
    - [ ] 所有章节内容完整
    - [ ] 标题层级使用正确
    - [ ] 代码示例可以运行
    - [ ] 图片显示正常
    - [ ] 内外部链接有效
    - [ ] 格式风格一致
    - [ ] 语法拼写正确
    
    ### Step 5: 发布维护 (持续改进)
    
    ```mermaid
    flowchart LR
        A[文档发布] --> B[收集反馈]
        B --> C[问题记录]
        C --> D[定期更新]
        D --> E[版本管理]
    ```
    
    **维护策略**：
    - **反馈机制**：建立用户反馈收集渠道
    - **更新计划**：制定定期更新和审核计划
    - **版本控制**：使用版本号管理文档变更
    - **协作流程**：建立多人协作的编辑流程
  </process>

  <criteria>
    ## 文档质量评价标准

    ### 内容质量
    - ✅ 信息准确性：技术细节正确无误
    - ✅ 内容完整性：涵盖所有必要信息点
    - ✅ 逻辑连贯性：章节间逻辑关系清晰
    - ✅ 实用性：读者能够实际应用

    ### 结构质量
    - ✅ 层次清晰：标题层级使用合理
    - ✅ 导航便利：目录和链接完善
    - ✅ 查找高效：信息组织便于检索
    - ✅ 视觉友好：排版美观易读

    ### 技术质量
    - ✅ 格式规范：符合Markdown标准
    - ✅ 兼容性好：跨平台显示一致
    - ✅ 性能优良：加载速度快
    - ✅ 维护性强：易于更新和协作

    ### 用户体验
    - ✅ 易于理解：语言表达清晰
    - ✅ 快速上手：提供快速开始指南
    - ✅ 问题解决：包含常见问题解答
    - ✅ 持续改进：根据反馈优化内容
  </criteria>
</execution>
