# AI小说助手开发文档 V2.0

## 目录

1. [项目概述](#1-项目概述)
2. [技术架构](#2-技术架构)
3. [项目结构](#3-项目结构)
4. [核心功能模块](#4-核心功能模块)
5. [界面设计规范](#5-界面设计规范)
6. [功能界面布局](#6-功能界面布局)
7. [功能详细说明](#7-功能详细说明)
8. [数据库设计](#8-数据库设计)
9. [API集成方案](#9-api集成方案)
10. [开发路线规划](#10-开发路线规划)
11. [环境搭建指南](#11-环境搭建指南)
12. [打包部署方案](#12-打包部署方案)
13. [创作流程指南](#13-创作流程指南)

---

## 1. 项目概述

### 1.1 项目简介

AI小说助手是一款基于Tauri + Python技术栈开发的桌面应用程序，专为网络小说创作者设计。应用内置所有依赖，无需用户手动安装复杂的开发环境，一键启动即可使用。

### 1.2 核心特性

- **多AI模型支持**：集成GPT、Claude、Gemini、ModelScope、Ollama、SiliconFlow等主流AI模型
- **智能大纲生成**：基于用户设定自动生成完整的小说大纲结构
- **上下文衔接**：自动检索和衔接章节间的上下文关系和伏笔
- **角色管理**：完整的人物设定和关系图管理系统
- **章节分析**：深度分析章节内容，提供改进建议
- **降AI味处理**：内置算法减少AI生成内容的机械化特征
- **Glassmorphism设计**：现代化的毛玻璃UI设计风格
- **内置依赖**：所有依赖打包在应用内，用户无需额外安装

### 1.3 目标用户

- 网络小说创作者（番茄小说、飞卢、17K、起点、纵横、晋江、七猫等平台作者）
- 业余写作爱好者
- 需要AI辅助创作的内容创作者
- 不熟悉技术环境配置的普通用户

### 1.4 应用优势

- **零配置启动**：内置所有依赖，下载即用
- **多平台支持**：Windows、macOS、Linux全平台兼容
- **离线可用**：支持本地Ollama模型，无需网络连接
- **数据安全**：所有数据本地存储，保护创作隐私
- **界面友好**：Glassmorphism设计风格，操作直观简洁

---

## 2. 技术架构

### 2.1 核心技术栈

#### 2.1.1 桌面端框架
- **Tauri 2.0**：基于Rust的轻量级桌面应用框架
  - 应用体积小（<50MB）
  - 内存占用低
  - 原生性能
  - 跨平台支持

#### 2.1.2 后端技术
- **Python 3.11+**：核心业务逻辑
- **FastAPI**：高性能API框架
- **SQLite**：轻量级数据库
- **SQLAlchemy**：ORM框架
- **Pydantic**：数据验证

#### 2.1.3 前端技术
- **Vue 3**：现代化前端框架
- **TypeScript**：类型安全的JavaScript
- **Vite**：快速构建工具
- **Element Plus**：UI组件库
- **Pinia**：状态管理

#### 2.1.4 AI集成
- **OpenAI SDK**：GPT模型集成
- **Anthropic SDK**：Claude模型集成
- **Google AI SDK**：Gemini模型集成
- **Requests**：HTTP客户端
- **Ollama API**：本地模型支持

### 2.2 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Desktop Shell                      │
├─────────────────────────────────────────────────────────────┤
│                     Vue 3 Frontend                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   UI组件    │ │   状态管理   │ │   路由管理   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                   Python Backend                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  API服务    │ │  业务逻辑    │ │  数据处理    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  SQLite DB  │ │  文件存储    │ │  配置管理    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                   AI Integration                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  OpenAI     │ │   Claude    │ │   Gemini    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ ModelScope  │ │   Ollama    │ │SiliconFlow  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. 项目结构

### 3.1 根目录结构

```
ai-novel-assistant/
├── src-tauri/                 # Tauri后端代码
│   ├── src/
│   │   ├── main.rs           # Tauri主入口
│   │   ├── commands.rs       # Tauri命令定义
│   │   └── lib.rs           # 库文件
│   ├── Cargo.toml           # Rust依赖配置
│   ├── tauri.conf.json      # Tauri配置文件
│   └── build.rs             # 构建脚本
├── src/                      # Vue前端代码
│   ├── components/          # Vue组件
│   ├── views/              # 页面视图
│   ├── stores/             # Pinia状态管理
│   ├── utils/              # 工具函数
│   ├── assets/             # 静态资源
│   ├── styles/             # 样式文件
│   ├── App.vue             # 根组件
│   └── main.ts             # 前端入口
├── python-backend/          # Python后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI主应用
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务服务
│   │   ├── utils/          # 工具函数
│   │   └── database/       # 数据库相关
│   ├── requirements.txt    # Python依赖
│   └── config.py          # 配置文件
├── public/                 # 公共资源
├── dist/                   # 构建输出
├── package.json           # Node.js依赖
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目说明
```

### 3.2 详细目录结构

#### 3.2.1 前端组件结构
```
src/components/
├── common/                  # 通用组件
│   ├── AppHeader.vue       # 应用头部
│   ├── AppSidebar.vue      # 侧边栏
│   ├── AppFooter.vue       # 底部状态栏
│   ├── LoadingSpinner.vue  # 加载动画
│   └── ConfirmDialog.vue   # 确认对话框
├── outline/                # 大纲相关组件
│   ├── OutlineGenerator.vue # 大纲生成器
│   ├── OutlineEditor.vue   # 大纲编辑器
│   └── OutlinePreview.vue  # 大纲预览
├── chapter/                # 章节相关组件
│   ├── ChapterList.vue     # 章节列表
│   ├── ChapterEditor.vue   # 章节编辑器
│   ├── ChapterGenerator.vue # 章节生成器
│   └── ChapterAnalyzer.vue # 章节分析器
├── character/              # 角色相关组件
│   ├── CharacterList.vue   # 角色列表
│   ├── CharacterEditor.vue # 角色编辑器
│   └── RelationshipGraph.vue # 关系图
├── ai/                     # AI相关组件
│   ├── ModelSelector.vue   # 模型选择器
│   ├── ChatInterface.vue   # 聊天界面
│   └── PromptLibrary.vue   # 提示词库
└── settings/               # 设置相关组件
    ├── ApiSettings.vue     # API设置
    ├── ThemeSettings.vue   # 主题设置
    └── GeneralSettings.vue # 通用设置
```

#### 3.2.2 Python后端结构
```
python-backend/app/
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── novel.py           # 小说模型
│   ├── chapter.py         # 章节模型
│   ├── character.py       # 角色模型
│   ├── outline.py         # 大纲模型
│   └── settings.py        # 设置模型
├── services/              # 业务服务
│   ├── __init__.py
│   ├── ai_service.py      # AI服务
│   ├── novel_service.py   # 小说服务
│   ├── chapter_service.py # 章节服务
│   ├── character_service.py # 角色服务
│   └── analysis_service.py # 分析服务
├── utils/                 # 工具函数
│   ├── __init__.py
│   ├── ai_utils.py        # AI工具
│   ├── text_utils.py      # 文本处理
│   ├── file_utils.py      # 文件处理
│   └── validation.py      # 数据验证
├── database/              # 数据库相关
│   ├── __init__.py
│   ├── connection.py      # 数据库连接
│   ├── migrations/        # 数据库迁移
│   └── seeds/             # 初始数据
└── api/                   # API路由
    ├── __init__.py
    ├── novel.py           # 小说API
    ├── chapter.py         # 章节API
    ├── character.py       # 角色API
    ├── ai.py              # AI API
    └── settings.py        # 设置API
```

---

## 4. 核心功能模块

### 4.1 功能模块概览

| 模块名称 | 功能描述 | 优先级 |
|---------|---------|--------|
| 大纲生成 | 基于AI模型生成小说大纲 | P0 |
| 大纲编辑 | 编辑和完善小说大纲 | P0 |
| 章节编辑 | 管理和编辑章节内容 | P0 |
| 章节生成 | AI辅助生成章节内容 | P0 |
| 章节分析 | 分析章节质量和改进建议 | P1 |
| 人物编辑 | 创建和管理角色信息 | P1 |
| 人物关系图 | 可视化角色关系 | P1 |
| 统计信息 | 显示创作进度和统计 | P2 |
| AI聊天 | 与AI模型对话交流 | P2 |
| 提示词库 | 管理和使用提示词模板 | P2 |
| 上下文管理 | 管理创作上下文信息 | P3 |
| 向量库检索 | 基于向量的内容检索 | P3 |
| 设置 | 应用配置和API设置 | P0 |

### 4.2 模块依赖关系

```
设置模块 (API配置)
    ↓
AI服务层 (模型调用)
    ↓
┌─────────────┬─────────────┬─────────────┐
│  大纲生成    │  章节生成    │  人物编辑    │
└─────────────┴─────────────┴─────────────┘
    ↓              ↓              ↓
┌─────────────┬─────────────┬─────────────┐
│  大纲编辑    │  章节编辑    │ 人物关系图   │
└─────────────┴─────────────┴─────────────┘
    ↓              ↓              ↓
┌─────────────┬─────────────┬─────────────┐
│  章节分析    │  统计信息    │  AI聊天     │
└─────────────┴─────────────┴─────────────┘
    ↓              ↓              ↓
┌─────────────┬─────────────┬─────────────┐
│  提示词库    │ 上下文管理   │ 向量库检索   │
└─────────────┴─────────────┴─────────────┘
```

---

## 5. 界面设计规范

### 5.1 设计理念

#### 5.1.1 Glassmorphism设计风格
- **毛玻璃效果**：使用半透明背景和模糊效果
- **层次感**：通过阴影和边框营造深度
- **现代感**：简洁的线条和圆角设计
- **透明度**：适度的透明效果增强视觉层次

#### 5.1.2 设计原则
- **用户体验至上**：界面操作直观简洁
- **功能性优先**：设计服务于功能需求
- **一致性**：统一的视觉语言和交互模式
- **可访问性**：支持不同用户群体的使用需求

### 5.2 色彩系统

#### 5.2.1 主题色彩

**明亮主题**
```css
:root {
  /* 主色调 */
  --primary-color: #3b82f6;      /* 蓝色 */
  --secondary-color: #10b981;    /* 绿色 */
  --accent-color: #f59e0b;       /* 橙色 */

  /* 背景色 */
  --bg-primary: rgba(255, 255, 255, 0.8);
  --bg-secondary: rgba(248, 250, 252, 0.9);
  --bg-glass: rgba(255, 255, 255, 0.1);

  /* 文本色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;

  /* 边框色 */
  --border-color: rgba(229, 231, 235, 0.3);
  --border-focus: rgba(59, 130, 246, 0.5);
}
```

**暗黑主题**
```css
:root[data-theme="dark"] {
  /* 主色调 */
  --primary-color: #60a5fa;      /* 亮蓝色 */
  --secondary-color: #34d399;    /* 亮绿色 */
  --accent-color: #fbbf24;       /* 亮橙色 */

  /* 背景色 */
  --bg-primary: rgba(17, 24, 39, 0.8);
  --bg-secondary: rgba(31, 41, 55, 0.9);
  --bg-glass: rgba(0, 0, 0, 0.2);

  /* 文本色 */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;

  /* 边框色 */
  --border-color: rgba(75, 85, 99, 0.3);
  --border-focus: rgba(96, 165, 250, 0.5);
}
```

#### 5.2.2 功能色彩
```css
/* 状态色彩 */
--success-color: #10b981;    /* 成功 */
--warning-color: #f59e0b;    /* 警告 */
--error-color: #ef4444;      /* 错误 */
--info-color: #3b82f6;       /* 信息 */

/* 按钮色彩 */
--btn-primary: linear-gradient(135deg, #3b82f6, #1d4ed8);
--btn-secondary: linear-gradient(135deg, #10b981, #059669);
--btn-warning: linear-gradient(135deg, #f59e0b, #d97706);
--btn-danger: linear-gradient(135deg, #ef4444, #dc2626);
```

### 5.3 字体系统

#### 5.3.1 字体族
```css
/* 主字体 */
--font-family-primary: 'Inter', 'Noto Sans SC', sans-serif;
/* 代码字体 */
--font-family-mono: 'JetBrains Mono', 'Consolas', monospace;
/* 标题字体 */
--font-family-heading: 'Inter', 'Noto Sans SC', sans-serif;
```

#### 5.3.2 字体大小
```css
--font-size-xs: 0.75rem;     /* 12px */
--font-size-sm: 0.875rem;    /* 14px */
--font-size-base: 1rem;      /* 16px */
--font-size-lg: 1.125rem;    /* 18px */
--font-size-xl: 1.25rem;     /* 20px */
--font-size-2xl: 1.5rem;     /* 24px */
--font-size-3xl: 1.875rem;   /* 30px */
--font-size-4xl: 2.25rem;    /* 36px */
```

### 5.4 间距系统

#### 5.4.1 基础间距
```css
--spacing-1: 0.25rem;   /* 4px */
--spacing-2: 0.5rem;    /* 8px */
--spacing-3: 0.75rem;   /* 12px */
--spacing-4: 1rem;      /* 16px */
--spacing-5: 1.25rem;   /* 20px */
--spacing-6: 1.5rem;    /* 24px */
--spacing-8: 2rem;      /* 32px */
--spacing-10: 2.5rem;   /* 40px */
--spacing-12: 3rem;     /* 48px */
--spacing-16: 4rem;     /* 64px */
```

#### 5.4.2 组件间距
- **卡片内边距**：24px
- **按钮内边距**：12px 24px
- **输入框内边距**：12px 16px
- **模块间距**：32px
- **元素间距**：16px

### 5.5 布局系统

#### 5.5.1 网格系统
- **容器最大宽度**：1200px
- **栅格列数**：24列
- **间隙**：16px
- **断点**：xs(480px), sm(768px), md(1024px), lg(1200px)

#### 5.5.2 布局比例
- **左右分栏布局**：40% : 60%（功能区 : 内容区）
- **三栏布局**：25% : 50% : 25%
- **侧边栏宽度**：280px
- **头部高度**：64px
- **底部高度**：32px

### 5.6 组件设计规范

#### 5.6.1 按钮规范
```css
/* 主要按钮 */
.btn-primary {
  background: var(--btn-primary);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  backdrop-filter: blur(10px);
}
```

#### 5.6.2 输入框规范
```css
.input-field {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 5.6.3 卡片规范
```css
.card {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

---

## 6. 功能界面布局

### 6.1 主界面布局（首页仪表盘）

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              应用标题栏                                          │
│  [应用图标] AI小说助手 v2.0                         [最小化] [最大化] [关闭]      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                顶部工具栏                                        │
│  [新建项目] [打开项目] [保存项目] [导入项目] [导出项目] [项目设置] [帮助文档]      │
├──────────────────┬──────────────────────────────────────────────────────────────┤
│                  │                        主要内容区域                          │
│   左侧功能导航    │  ┌─────────────────────────────────────────────────────────┐  │
│                  │  │                    项目概览面板                         │  │
│  [大纲生成图标] 大纲生成      │  │                                                         │  │
│  [大纲编辑图标] 大纲编辑      │  │  当前项目：[未打开项目]                                 │  │
│  [章节编辑图标] 章节编辑      │  │  项目路径：[无]                                         │  │
│  [章节生成图标] 章节生成      │  │  创建时间：[无]                                         │  │
│  [章节分析图标] 章节分析      │  │  最后修改：[无]                                         │  │
│  [人物编辑图标] 人物编辑      │  │  项目状态：[未开始]                                     │  │
│  [关系图图标] 人物关系图      │  │                                                         │  │
│  [统计信息图标] 统计信息      │  │  ┌─────────────┬─────────────┬─────────────┐           │  │
│  [AI聊天图标] AI聊天         │  │  │   总章节数   │   总字数     │   完成进度   │           │  │
│  [提示词库图标] 提示词库      │  │  │      0      │      0      │     0%      │           │  │
│  [上下文管理图标] 上下文管理  │  │  └─────────────┴─────────────┴─────────────┘           │  │
│  [向量检索图标] 向量库检索    │  │                                                         │  │
│  [设置图标] 设置              │  │  ┌─────────────┬─────────────┬─────────────┐           │  │
│                  │  │  │   大纲状态   │   角色数量   │   AI模型     │           │  │
│  ────────────    │  │  │    未创建    │      0      │    未配置    │           │  │
│                  │  │  └─────────────┴─────────────┴─────────────┘           │  │
│                  │  └─────────────────────────────────────────────────────────┘  │
│                  │                                                              │
│                  │  ┌─────────────────────────────────────────────────────────┐  │
│                  │  │                    快捷操作面板                         │  │
│                  │  │                                                         │  │
│                  │  │  ┌─────────────┬─────────────┬─────────────┐           │  │
│                  │  │  │ [新建项目]   │ [打开项目]   │ [项目模板]   │           │  │
│                  │  │  └─────────────┴─────────────┴─────────────┘           │  │
│                  │  │                                                         │  │
│                  │  │  ┌─────────────┬─────────────┬─────────────┐           │  │
│                  │  │  │ [AI模型配置] │ [提示词库]   │ [使用教程]   │           │  │
│                  │  │  └─────────────┴─────────────┴─────────────┘           │  │
│                  │  └─────────────────────────────────────────────────────────┘  │
│                  │                                                              │
│                  │  ┌─────────────────────────────────────────────────────────┐  │
│                  │  │                    最近项目列表                         │  │
│                  │  │                                                         │  │
│                  │  │  [项目历史图标] 项目历史记录                             │  │
│                  │  │  ┌─────────────────────────────────────────────────┐   │  │
│                  │  │  │                                                 │   │  │
│                  │  │  │            [暂无最近项目]                        │   │  │
│                  │  │  │                                                 │   │  │
│                  │  │  │         点击"新建项目"开始创作                   │   │  │
│                  │  │  │                                                 │   │  │
│                  │  │  └─────────────────────────────────────────────────┘   │  │
│                  │  │                                                         │  │
│                  │  │  [清空历史] [导入项目] [项目备份管理]                    │  │
│                  │  └─────────────────────────────────────────────────────────┘  │
├──────────────────┴──────────────────────────────────────────────────────────────┤
│                                底部状态栏                                        │
│  状态：就绪 | AI连接：未配置 | 项目：未打开 | 内存使用：[显示] | 版本：v2.0        │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 6.2 大纲生成界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                     [大纲生成图标] 大纲生成 - AI智能大纲创作                     │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧功能配置区    │                    右侧生成结果区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [AI模型图标] AI模型配置   │ │  │                                                     │ │
│  │                 │ │  │              [大纲结果图标] 大纲生成结果                        │ │
│  │ 当前模型：       │ │  │                                                     │ │
│  │ [选择AI模型 ▼]  │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │                                             │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [测试连接]       │ │  │  │                                             │   │ │
│  │ [模型设置]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [提示词图标] 提示词配置   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 提示词模板：     │ │  │  │                                             │   │ │
│  │ [选择模板 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 模板类型：       │ │  │  │                                             │   │ │
│  │ ○ 标准大纲       │ │  │  │                                             │   │ │
│  │ ○ 详细大纲       │ │  │  │                                             │   │ │
│  │ ○ 简化大纲       │ │  │  │                                             │   │ │
│  │ ○ 自定义模板     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [编辑模板]       │ │  │  │                                             │   │ │
│  │ [新建模板]       │ │  │  │                                             │   │ │
│  │ [导入模板]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [基本信息图标] 基本信息设置 │ │  │  生成状态：[等待生成]                               │ │
│  │                 │ │  │                                                     │ │
│  │ 小说标题：       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [____________]  │ │  │  │              [进度图标] 生成进度                    │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 小说类型：       │ │  │  │  当前步骤：[未开始]                         │   │ │
│  │ [选择类型 ▼]    │ │  │  │  进度条：[                    ] 0%          │   │ │
│  │                 │ │  │  │  预计时间：[计算中]                         │   │ │
│  │ 小说主题：       │ │  │  │                                             │   │ │
│  │ [选择主题 ▼]    │ │  │  │  [暂停生成] [停止生成] [重新生成]            │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 小说风格：       │ │  │                                                     │ │
│  │ [选择风格 ▼]    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  │ 目标读者：       │ │  │  │                                             │   │ │
│  │ [选择读者 ▼]    │ │  │  │  [保存大纲] [导出大纲] [预览大纲]            │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │  [复制内容] [清空重置] [生成报告]            │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [章节图标] 章节配置     │ │  │  │  [应用到项目] [另存为模板]                   │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 总章节数：       │ │  │                                                     │ │
│  │ [____] 章       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [质量图标] 质量评估                    │   │ │
│  │ 每章字数：       │ │  │  │                                             │   │ │
│  │ [____] 字       │ │  │  │  结构完整性：[未评估]                       │   │ │
│  │                 │ │  │  │  逻辑连贯性：[未评估]                       │   │ │
│  │ 生成范围：       │ │  │  │  创意新颖性：[未评估]                       │   │ │
│  │ 从第[__]章      │ │  │  │  可读性评分：[未评估]                       │   │ │
│  │ 到第[__]章      │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │  综合评分：[等待评估]                       │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │  [开始评估] [详细报告]                       │   │ │
│  │  [人物配置图标] 人物配置     │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  └─────────────────────────────────────────────────────┘ │
│  │ 主角数量：[_]    │ │                                                         │
│  │ 重要角色：[_]    │ │                                                         │
│  │ 配角数量：[_]    │ │                                                         │
│  │ 龙套数量：[_]    │ │                                                         │
│  └─────────────────┘ │                                                         │
│                     │                                                         │
│  ┌─────────────────┐ │                                                         │
│  │  [生成控制图标] 生成控制     │ │                                                         │
│  │                 │ │                                                         │
│  │ 生成模式：       │ │                                                         │
│  │ ○ 完整生成       │ │                                                         │
│  │ ○ 分步生成       │ │                                                         │
│  │ ○ 续写模式       │ │                                                         │
│  │                 │ │                                                         │
│  │ 创意程度：       │ │                                                         │
│  │ ●●●○○           │ │                                                         │
│  │                 │ │                                                         │
│  │ [开始生成大纲]   │ │                                                         │
│  │ [清空重置]       │ │                                                         │
│  │ [保存配置]       │ │                                                         │
│  │                 │ │                                                         │
│  │ 生成状态：       │ │                                                         │
│  │ [等待开始]       │ │                                                         │
│  └─────────────────┘ │                                                         │
│                     │                                                         │
│  ┌─────────────────┐ │                                                         │
│  │  [统计图标] 生成统计     │ │                                                         │
│  │                 │ │                                                         │
│  │ 本次生成：       │ │                                                         │
│  │ 字数：[0]        │ │                                                         │
│  │ 章节：[0]        │ │                                                         │
│  │ 角色：[0]        │ │                                                         │
│  │                 │ │                                                         │
│  │ 历史统计：       │ │                                                         │
│  │ 总生成次数：[0]  │ │                                                         │
│  │ 成功率：[0%]     │ │                                                         │
│  │                 │ │                                                         │
│  │ [查看详情]       │ │                                                         │
│  └─────────────────┘ │                                                         │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.3 大纲编辑界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [大纲编辑图标] 大纲编辑 - 智能大纲完善工具                    │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧编辑功能区    │                    右侧编辑内容区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [版本图标] 版本管理     │ │  │                                                     │ │
│  │                 │ │  │              [标签页图标] 编辑标签页导航                      │ │
│  │ 当前版本：       │ │  │  [基本信息] [故事梗概] [世界观] [章节大纲] [角色]    │ │
│  │ [未保存]         │ │  │                                                     │ │
│  │                 │ │  ├─────────────────────────────────────────────────────┤ │
│  │ 版本历史：       │ │  │                                                     │ │
│  │ [暂无版本]       │ │  │                   [基本信息图标] 基本信息编辑                   │ │
│  │                 │ │  │                                                     │ │
│  │ [保存新版本]     │ │  │  小说标题：                                         │ │
│  │ [版本对比]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [恢复版本]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │  [AI优化标题] [标题建议] [检查重复]                 │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [AI辅助图标] AI辅助工具   │ │  │  中心思想：                                         │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ AI模型：         │ │  │  │                                             │   │ │
│  │ [选择模型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 连接状态：       │ │  │  [AI完善思想] [思想分析] [主题建议]                 │ │
│  │ [未连接]         │ │  │                                                     │ │
│  │                 │ │  │  小说类型：                                         │ │
│  │ 智能功能：       │ │  │  [选择类型 ▼]  风格：[选择风格 ▼]                  │ │
│  │ [优化标题]       │ │  │                                                     │ │
│  │ [完善主题]       │ │  │  目标读者：[选择读者 ▼]  篇幅：[选择篇幅 ▼]        │ │
│  │ [扩展梗概]       │ │  │                                                     │ │
│  │ [丰富世界观]     │ │  │  章节设置：                                         │ │
│  │ [角色分析]       │ │  │  总章节数：[____] 章  每章字数：[____] 字           │ │
│  │ [情节检查]       │ │  │                                                     │ │
│  └─────────────────┘ │  │  故事梗概：                                         │ │
│                     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [快速操作图标] 快速操作     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 文件操作：       │ │  │  │                                             │   │ │
│  │ [导入大纲]       │ │  │  │                                             │   │ │
│  │ [导出大纲]       │ │  │  │                                             │   │ │
│  │ [另存为]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 编辑操作：       │ │  │  │                                             │   │ │
│  │ [预览效果]       │ │  │  │                                             │   │ │
│  │ [保存修改]       │ │  │  │                                             │   │ │
│  │ [重置内容]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │  [AI扩展梗概] [语法检查] [内容优化]                 │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [编辑统计图标] 编辑统计     │ │  │  世界观设定：                                       │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ 修改次数：[0]    │ │  │  │                                             │   │ │
│  │ 字数统计：[0]    │ │  │  │                                             │   │ │
│  │ 最后修改：       │ │  │  │                                             │   │ │
│  │ [未修改]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 编辑进度：       │ │  │  │                                             │   │ │
│  │ [未开始]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [详细统计]       │ │  │  │                                             │   │ │
│  │ [导出报告]       │ │  │  └─────────────────────────────────────────────┘   │ │
│  └─────────────────┘ │  │  [AI丰富世界观] [设定检查] [逻辑验证]               │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  章节大纲：                                         │ │
│  │  [工具面板图标] 工具面板     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [格式化文本]     │ │  │  │                                             │   │ │
│  │ [检查语法]       │ │  │  │                                             │   │ │
│  │ [内容统计]       │ │  │  │                                             │   │ │
│  │ [查找替换]       │ │  │  │                                             │   │ │
│  │ [自动保存]       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 自动保存：       │ │  │  │                                             │   │ │
│  │ ☑ 启用          │ │  │  │                                             │   │ │
│  │ 间隔：[5分钟]    │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │  [AI生成章节] [章节排序] [批量编辑]                 │ │
│                     │ │  │                                                     │ │
│                     │ │  │  [保存大纲] [发布大纲] [分享链接]                   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.4 章节编辑界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [章节编辑图标] 章节编辑 - 智能章节创作工具                    │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧章节管理区    │                    右侧编辑内容区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [导航图标] 章节导航     │ │  │                                                     │ │
│  │                 │ │  │              [编辑器图标] 章节编辑器                          │ │
│  │ 搜索章节：       │ │  │                                                     │ │
│  │ [____________]  │ │  │  当前章节：[未选择章节]                             │ │
│  │                 │ │  │                                                     │ │
│  │ 筛选条件：       │ │  │  章节标题：                                         │ │
│  │ ☑ 已完成        │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ☑ 编辑中        │ │  │  │                                             │   │ │
│  │ ☑ 未开始        │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │  [AI生成标题] [标题建议] [检查重复]                 │ │
│  │ 章节列表：       │ │  │                                                     │ │
│  │ [暂无章节]       │ │  │  章节类型：                                         │ │
│  │                 │ │  │  ○ 正文章节  ○ 过渡章节  ○ 高潮章节  ○ 结尾章节    │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  字数目标：[____] 字  当前字数：[0] 字              │ │
│  │                 │ │  │                                                     │ │
│  │ [新建章节]       │ │  │  章节摘要：                                         │ │
│  │ [批量导入]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [章节排序]       │ │  │  │                                             │   │ │
│  │ [批量操作]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  └─────────────────────────────────────────────┘   │ │
│  │  [AI写作图标] AI写作助手   │ │  │  [AI生成摘要] [摘要优化] [关键词提取]               │ │
│  │                 │ │  │                                                     │ │
│  │ AI模型：         │ │  │  章节内容：                                         │ │
│  │ [选择模型 ▼]    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │                                             │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 写作功能：       │ │  │  │                                             │   │ │
│  │ [生成标题]       │ │  │  │                                             │   │ │
│  │ [扩展摘要]       │ │  │  │                                             │   │ │
│  │ [续写内容]       │ │  │  │                                             │   │ │
│  │ [优化文本]       │ │  │  │                                             │   │ │
│  │ [检查逻辑]       │ │  │  │                                             │   │ │
│  │ [语法检查]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [模板管理图标] 模板管理     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 模板类型：       │ │  │  │                                             │   │ │
│  │ [选择模板 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 模板操作：       │ │  │  │                                             │   │ │
│  │ [应用模板]       │ │  │  │                                             │   │ │
│  │ [保存模板]       │ │  │  │                                             │   │ │
│  │ [管理模板]       │ │  │  │                                             │   │ │
│  │ [导入模板]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [设置图标] 编辑器设置   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 显示选项：       │ │  │  │                                             │   │ │
│  │ ☑ 显示行号       │ │  │  │                                             │   │ │
│  │ ☑ 自动保存       │ │  │  │                                             │   │ │
│  │ ☑ 语法高亮       │ │  │  │                                             │   │ │
│  │ ☐ 专注模式       │ │  │  │                                             │   │ │
│  │ ☐ 全屏编辑       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 字体设置：       │ │  │  │                                             │   │ │
│  │ 大小：[__px]     │ │  │  │                                             │   │ │
│  │ 行距：[___]      │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 保存设置：       │ │  │  │                                             │   │ │
│  │ 间隔：[_分钟]    │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │  [章节统计图标] 章节统计     │ │  │  │              [操作图标] 操作面板                    │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 当前字数：[0]    │ │  │  │  [保存章节] [预览效果] [导出章节]            │   │ │
│  │ 目标字数：[___]  │ │  │  │                                             │   │ │
│  │ 完成度：[0%]     │ │  │  │  [复制内容] [清空重置] [版本对比]            │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 段落数：[0]      │ │  │  │  [发布章节] [分享链接] [打印章节]            │   │ │
│  │ 句子数：[0]      │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │                                                     │ │
│  │ [详细统计]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  └─────────────────┘ │  │  │              [质量检测图标] 质量检测                    │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  可读性：[未检测]  语法：[未检测]            │   │ │
│                     │ │  │  │  逻辑性：[未检测]  创意性：[未检测]          │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [开始检测] [详细报告] [改进建议]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.5 章节生成界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [章节生成图标] 章节生成 - AI智能章节创作                      │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧生成设置区    │                    右侧生成内容区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [章节选择图标] 章节选择     │ │  │                                                     │ │
│  │                 │ │  │              [信息面板图标] 章节信息面板                        │ │
│  │ 当前章节：       │ │  │                                                     │ │
│  │ [选择章节 ▼]    │ │  │  章节序号：[未选择]                                 │ │
│  │                 │ │  │  章节标题：[未设置]                                 │ │
│  │ 章节状态：       │ │  │  字数目标：[未设置] 字                               │ │
│  │ [未开始]         │ │  │  当前字数：[0] 字                                   │ │
│  │                 │ │  │  完成进度：[0%]                                     │ │
│  │ 章节导航：       │ │  │                                                     │ │
│  │ [上一章] [下一章]│ │  │  创建时间：[未创建]                                 │ │
│  │ [章节列表]       │ │  │  最后修改：[未修改]                                 │ │
│  └─────────────────┘ │  ├─────────────────────────────────────────────────────┤ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │                   [内容生成图标] 章节内容生成区                 │ │
│  │  [AI设置图标] AI生成设置   │ │  │                                                     │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ AI模型：         │ │  │  │                                             │   │ │
│  │ [选择模型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │                                             │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 生成参数：       │ │  │  │                                             │   │ │
│  │ 目标字数：       │ │  │  │                                             │   │ │
│  │ [____] 字       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 创意程度：       │ │  │  │                                             │   │ │
│  │ ●●●○○           │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 生成模式：       │ │  │  │                                             │   │ │
│  │ ○ 完整生成       │ │  │  │                                             │   │ │
│  │ ○ 续写模式       │ │  │  │                                             │   │ │
│  │ ○ 分段生成       │ │  │  │                                             │   │ │
│  │ ○ 智能续写       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [上下文图标] 上下文设置   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 引用内容：       │ │  │  │                                             │   │ │
│  │ ☑ 包含大纲       │ │  │  │                                             │   │ │
│  │ ☑ 前章内容       │ │  │  │                                             │   │ │
│  │ ☑ 角色信息       │ │  │  │                                             │   │ │
│  │ ☑ 世界观设定     │ │  │  │                                             │   │ │
│  │ ☐ 写作风格       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 上下文长度：     │ │  │  │                                             │   │ │
│  │ [____] 字       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [AI辅助编辑图标] AI辅助编辑   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 智能功能：       │ │  │  │                                             │   │ │
│  │ [选定文本润色]   │ │  │  │                                             │   │ │
│  │ [扩展内容]       │ │  │  │                                             │   │ │
│  │ [调整语调]       │ │  │  │                                             │   │ │
│  │ [检查逻辑]       │ │  │  │                                             │   │ │
│  │ [语法检查]       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 选中字数：[0]    │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │  [生成控制图标] 生成控制     │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 生成操作：       │ │  │  │  [保存章节] [预览效果] [导出文档]            │   │ │
│  │ [开始生成]       │ │  │  │                                             │   │ │
│  │ [暂停生成]       │ │  │  │  [复制内容] [清空重置] [版本对比]            │   │ │
│  │ [停止生成]       │ │  │  │                                             │   │ │
│  │ [重新生成]       │ │  │  │  [发布章节] [分享链接] [打印文档]            │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 生成状态：       │ │  │                                                     │ │
│  │ [等待开始]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [生成进度图标] 生成进度                    │   │ │
│  │ 生成进度：       │ │  │  │                                             │   │ │
│  │ [进度条] 0%      │ │  │  │  当前步骤：[未开始]                         │   │ │
│  │                 │ │  │  │  进度条：[                    ] 0%          │   │ │
│  │ 预计时间：       │ │  │  │  已生成：[0] 字 / 目标：[0] 字              │   │ │
│  │ [计算中]         │ │  │  │  预计完成：[计算中]                         │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │  [查看详情] [生成日志] [质量评估]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.6 章节分析界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [章节分析图标] 章节分析 - AI智能章节分析工具                  │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧分析配置区    │                    右侧分析结果区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [章节选择图标] 章节选择     │ │  │                                                     │ │
│  │                 │ │  │              [分析报告图标] 分析报告概览                        │ │
│  │ 分析目标：       │ │  │                                                     │ │
│  │ [选择章节 ▼]    │ │  │  章节信息：[未选择章节]                             │ │
│  │                 │ │  │  章节字数：[0] 字                                   │ │
│  │ 章节状态：       │ │  │  分析时间：[未分析]                                 │ │
│  │ [未选择]         │ │  │  分析状态：[等待分析]                               │ │
│  │                 │ │  │                                                     │ │
│  │ 章节信息：       │ │  │  分析模型：[未配置]                                 │ │
│  │ 字数：[0]        │ │  │  分析深度：[标准]                                   │ │
│  │ 创建：[未知]     │ │  │                                                     │ │
│  │ 修改：[未知]     │ │  ├─────────────────────────────────────────────────────┤ │
│  └─────────────────┘ │  │                                                     │ │
│                     │ │  │                   [分析内容图标] 分析内容展示                   │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [分析选项图标] 分析选项     │ │  │  [核心剧情] [故事梗概] [优缺点] [角色标注] [改进建议] │ │
│  │                 │ │  │                                                     │ │
│  │ 分析维度：       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ☑ 核心剧情分析   │ │  │  │                                             │   │ │
│  │ ☑ 故事梗概提取   │ │  │  │              [分析结果为空]                  │   │ │
│  │ ☑ 优缺点分析     │ │  │  │                                             │   │ │
│  │ ☑ 角色行为标注   │ │  │  │          请选择章节并开始分析                │   │ │
│  │ ☑ 物品道具标注   │ │  │  │                                             │   │ │
│  │ ☑ 改进建议生成   │ │  │  │                                             │   │ │
│  │ ☑ 语言风格分析   │ │  │  │                                             │   │ │
│  │ ☑ 情节连贯性     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 快速选择：       │ │  │  │                                             │   │ │
│  │ [全选] [全不选]  │ │  │  │                                             │   │ │
│  │ [基础分析]       │ │  │  │                                             │   │ │
│  │ [深度分析]       │ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │   AI模型配置     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 分析模型：       │ │  │  │                                             │   │ │
│  │ [选择模型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 分析深度：       │ │  │  │                                             │   │ │
│  │ ○ 简要分析       │ │  │  │                                             │   │ │
│  │ ● 详细分析       │ │  │  │                                             │   │ │
│  │ ○ 深度分析       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [AI模型配置图标] AI模型配置   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 分析模型：       │ │  │  │                                             │   │ │
│  │ [选择模型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │                                             │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [分析控制图标] 分析控制     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [开始分析]       │ │  │  │                                             │   │ │
│  │ [重新分析]       │ │  │  │                                             │   │ │
│  │ [暂停分析]       │ │  │  │                                             │   │ │
│  │ [导出报告]       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 分析进度：       │ │  │  │                                             │   │ │
│  │ [进度条] 0%      │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│                     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│                     │ │  │  │              [改进工具图标] 改进工具                    │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [应用建议] [手动改进] [AI辅助改进]          │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [导出报告] [保存分析] [分享结果]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.7 人物编辑界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [人物编辑图标] 人物编辑 - 智能角色创作工具                    │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧角色管理区    │                    右侧编辑内容区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [角色列表图标] 角色列表     │ │  │                                                     │ │
│  │                 │ │  │              [角色编辑器图标] 角色编辑器                          │ │
│  │ 搜索角色：       │ │  │                                                     │ │
│  │ [____________]  │ │  │  当前角色：[未选择角色]                             │ │
│  │                 │ │  │                                                     │ │
│  │ 分类筛选：       │ │  │  [基本信息] [外貌特征] [性格特点] [背景经历] [关系网络] │ │
│  │ [选择分类 ▼]    │ │  │                                                     │ │
│  │                 │ │  ├─────────────────────────────────────────────────────┤ │
│  │ 角色分类：       │ │  │                                                     │ │
│  │ ├ [主角图标] 主角        │ │  │                   [基本信息图标] 基本信息                       │ │
│  │ ├ [重要角色图标] 重要角色     │ │  │                                                     │ │
│  │ ├ [配角图标] 配角        │ │  │  角色姓名：                                         │ │
│  │ └ [龙套图标] 龙套        │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 角色列表：       │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ [暂无角色]       │ │  │  [AI生成姓名] [姓名建议] [检查重复]                 │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  角色类型：                                         │ │
│  │                 │ │  │  ○ 主角  ○ 重要角色  ○ 配角  ○ 龙套               │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  性别：                                             │ │
│  │                 │ │  │  ○ 男  ○ 女  ○ 其他                               │ │
│  │                 │ │  │                                                     │ │
│  │ 角色操作：       │ │  │  年龄：[____] 岁  出生地：[____________]            │ │
│  │ [新建角色]       │ │  │                                                     │ │
│  │ [批量导入]       │ │  │  职业/身份：                                        │ │
│  │ [导出角色]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [角色模板]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │  [AI生成职业] [职业建议] [相关技能]                 │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [AI辅助工具图标] AI辅助工具   │ │  │  角色简介：                                         │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ AI模型：         │ │  │  │                                             │   │ │
│  │ [选择模型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │                                             │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 智能功能：       │ │  │  │                                             │   │ │
│  │ [生成角色]       │ │  │  │                                             │   │ │
│  │ [完善设定]       │ │  │  │                                             │   │ │
│  │ [生成背景]       │ │  │  │                                             │   │ │
│  │ [角色对话]       │ │  │  │                                             │   │ │
│  │ [性格分析]       │ │  │  └─────────────────────────────────────────────┘   │ │
│  └─────────────────┘ │  │  [AI扩展简介] [语言风格] [个性标签]                 │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  角色标签：                                         │ │
│  │  [模板管理图标] 模板管理     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 角色模板：       │ │  │  │  [暂无标签]                                 │   │ │
│  │ [选择模板 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 模板操作：       │ │  │  [添加标签] [智能标签] [标签管理]                   │ │
│  │ [应用模板]       │ │  │                                                     │ │
│  │ [保存模板]       │ │  │  重要程度：                                         │ │
│  │ [管理模板]       │ │  │  ☆☆☆☆☆                                            │ │
│  │ [导入模板]       │ │  │                                                     │ │
│  └─────────────────┘ │  │  出场频率：                                         │ │
│                     │ │  │  ○ 经常  ○ 偶尔  ○ 很少                           │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [关系管理图标] 关系管理     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  │ 关系功能：       │ │  │  │                                             │   │ │
│  │ [查看关系图]     │ │  │  │  [保存角色] [删除角色] [复制角色]            │   │ │
│  │ [编辑关系]       │ │  │  │                                             │   │ │
│  │ [关系分析]       │ │  │  │  [预览角色] [导出角色] [角色报告]            │   │ │
│  │ [批量关系]       │ │  │  │                                             │   │ │
│  │                 │ │  │  │  [版本历史] [角色对比] [发布角色]            │   │ │
│  │ 关系统计：       │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 关系数量：[0]    │ │  │                                                     │ │
│  │ 冲突关系：[0]    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ 亲密关系：[0]    │ │  │  │              [角色统计图标] 角色统计                    │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [关系报告]       │ │  │  │  创建时间：[未创建]  字数统计：[0]           │   │ │
│  └─────────────────┘ │  │  │  最后修改：[未修改]  完整度：[0%]            │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [详细统计] [使用记录] [质量评估]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.8 人物关系图界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                  [关系图图标] 人物关系图 - 智能关系可视化工具                    │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧控制面板区    │                    右侧关系图显示区                      │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [人物筛选图标] 人物筛选     │ │  │                                                     │ │
│  │                 │ │  │              [关系图可视化图标] 关系图可视化区域                    │ │
│  │ 显示设置：       │ │  │                                                     │ │
│  │ 按类型筛选：     │ │  │                                                     │ │
│  │ ☑ 主角          │ │  │        ┌─────────────────────────────────┐           │ │
│  │ ☑ 重要角色       │ │  │        │                                 │           │ │
│  │ ☑ 配角          │ │  │        │        [关系图为空]              │           │ │
│  │ ☐ 龙套          │ │  │        │                                 │           │ │
│  │                 │ │  │        │     请先添加角色和关系           │           │ │
│  │ 按状态筛选：     │ │  │        │                                 │           │ │
│  │ ☑ 在场角色       │ │  │        │                                 │           │ │
│  │ ☑ 离场角色       │ │  │        │                                 │           │ │
│  │ ☐ 已死亡         │ │  │        │                                 │           │ │
│  │ ☐ 隐藏角色       │ │  │        │                                 │           │ │
│  │                 │ │  │        │                                 │           │ │
│  │ 快速筛选：       │ │  │        │                                 │           │ │
│  │ [全选] [全不选]  │ │  │        │                                 │           │ │
│  │ [重置筛选]       │ │  │        │                                 │           │ │
│  └─────────────────┘ │  │        │                                 │           │ │
│                     │ │  │        │                                 │           │ │
│  ┌─────────────────┐ │  │        │                                 │           │ │
│  │  🔗 关系筛选     │ │  │        │                                 │           │ │
│  │                 │ │  │        │                                 │           │ │
│  │ 关系类型：       │ │  │        │                                 │           │ │
│  │ ☑ 亲情          │ │  │        │                                 │           │ │
│  │ ☑ 爱情          │ │  │        │                                 │           │ │
│  │ ☑ 友情          │ │  │        │                                 │           │ │
│  │ ☑ 师徒          │ │  │        │                                 │           │ │
│  │ ☑ 敌对          │ │  │        │                                 │           │ │
│  │ ☐ 合作          │ │  │        │                                 │           │ │
│  │ ☐ 商业          │ │  │        │                                 │           │ │
│  │                 │ │  │        │                                 │           │ │
│  │ 关系强度：       │ │  │        │                                 │           │ │
│  │ ☑ 强关系        │ │  │        │                                 │           │ │
│  │ ☑ 中等关系      │ │  │        │                                 │           │ │
│  │ ☐ 弱关系        │ │  │        │                                 │           │ │
│  │                 │ │  │        │                                 │           │ │
│  │ [重置筛选]       │ │  │        └─────────────────────────────────┘           │ │
│  └─────────────────┘ │  │                                                     │ │
│                     │ │  │  [放大] [缩小] [适应窗口] [全屏显示]                 │ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [图形设置图标] 图形设置     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [工具栏图标] 工具栏                      │   │ │
│  │ 布局算法：       │ │  │  │                                             │   │ │
│  │ [选择布局 ▼]    │ │  │  │  [添加角色] [添加关系] [删除选中]            │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 视觉样式：       │ │  │  │  [自动布局] [手动调整] [保存布局]            │   │ │
│  │ 节点大小：       │ │  │  │                                             │   │ │
│  │ ●●●○○           │ │  │  │  [导出图片] [导出数据] [打印关系图]          │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 连线样式：       │ │  │                                                     │ │
│  │ ○ 直线 ○ 曲线    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [关系统计图标] 关系统计                    │   │ │
│  │ 颜色主题：       │ │  │  │                                             │   │ │
│  │ [默认主题 ▼]    │ │  │  │  角色总数：[0]    关系总数：[0]              │   │ │
│  │                 │ │  │  │  主角数量：[0]    配角数量：[0]              │   │ │
│  │ 标签显示：       │ │  │  │                                             │   │ │
│  │ ☑ 角色名称       │ │  │  │  亲情关系：[0]    爱情关系：[0]              │   │ │
│  │ ☑ 关系类型       │ │  │  │  友情关系：[0]    敌对关系：[0]              │   │ │
│  │ ☐ 关系强度       │ │  │  │                                             │   │ │
│  │ ☐ 角色类型       │ │  │  │  [详细统计] [关系报告] [数据分析]            │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────┐ │                                                         │
│  │  [视图控制图标] 视图控制     │ │                                                         │
│  │                 │                                                         │
│  │ 缩放控制：       │                                                         │
│  │ [+] [____%] [-] │                                                         │
│  │                 │                                                         │
│  │ 视图模式：       │                                                         │
│  │ ○ 全览 ○ 聚焦    │                                                         │
│  │ ○ 分层 ○ 网格    │                                                         │
│  │                 │                                                         │
│  │ 视图操作：       │                                                         │
│  │ [重置视图]       │                                                         │
│  │ [适应窗口]       │                                                         │
│  │ [居中显示]       │                                                         │
│  └─────────────────┘                                                         │
│                                                                             │
│  ┌─────────────────┐                                                         │
│  │  ✏️ 关系编辑     │                                                         │
│  │                 │                                                         │
│  │ 快速编辑：       │                                                         │
│  │ 角色1：          │                                                         │
│  │ [选择角色 ▼]    │                                                         │
│  │                 │                                                         │
│  │ 角色2：          │                                                         │
│  │ [选择角色 ▼]    │                                                         │
│  │                 │                                                         │
│  │ 关系类型：       │                                                         │
│  │ [选择关系 ▼]    │                                                         │
│  │                 │                                                         │
│  │ 关系强度：       │                                                         │
│  │ ☆☆☆☆☆           │                                                         │
│  │                 │                                                         │
│  │ 关系描述：       │                                                         │
│  │ [____________]  │                                                         │
│  │                 │                                                         │
│  │ 编辑操作：       │                                                         │
│  │ [添加关系]       │                                                         │
│  │ [修改关系]       │                                                         │
│  │ [删除关系]       │                                                         │
│  │ [批量编辑]       │                                                         │
│  │                 │                                                         │
│  │ [关系验证]       │                                                         │
│  │ [冲突检测]       │                                                         │
│  └─────────────────┘                                                         │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 6.9 统计信息界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [统计信息图标] 统计信息 - 智能数据分析面板                    │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧统计配置区    │                    右侧统计显示区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [统计类型图标] 统计类型     │ │  │                                                     │ │
│  │                 │ │  │              [统计报表图标] 统计报表面板                        │ │
│  │ 统计维度：       │ │  │                                                     │ │
│  │ ☑ 基础统计       │ │  │  [总体概览] [章节统计] [人物统计] [进度统计] [质量分析] │ │
│  │ ☑ 章节统计       │ │  │                                                     │ │
│  │ ☑ 人物统计       │ │  ├─────────────────────────────────────────────────────┤ │
│  │ ☑ 进度统计       │ │  │                                                     │ │
│  │ ☑ 时间统计       │ │  │                   [总体概览图标] 总体概览                       │ │
│  │ ☑ 质量分析       │ │  │                                                     │ │
│  │                 │ │  │  ┌─────────────┬─────────────┬─────────────┐       │ │
│  │ 快速选择：       │ │  │  │     概览     │   章节统计   │   完成进度   │       │ │
│  │ [全选] [全不选]  │ │  │  ├─────────────┼─────────────┼─────────────┤       │ │
│  │ [基础报表]       │ │  │  │ 小说标题：   │ 章节数：     │ 已完成章节： │       │ │
│  │ [详细报表]       │ │  │  │ [未设置]     │ [0]         │ [0]         │       │ │
│  └─────────────────┘ │  │  │             │             │             │       │ │
│                     │ │  │  │ 总字数：     │ 平均每章字数：│ 完成度：     │       │ │
│  ┌─────────────────┐ │  │  │ [0]         │ [0]         │ [0%]        │       │ │
│  │  📅 统计范围     │ │  │  │             │             │             │       │ │
│  │                 │ │  │  │ 创建时间：   │ 最长章节：   │ 预计完成：   │       │ │
│  │ 时间范围：       │ │  │  │ [未创建]     │ [0字]       │ [未知]       │       │ │
│  │ [选择范围 ▼]    │ │  │  └─────────────┴─────────────┴─────────────┘       │ │
│  │                 │ │  │                                                     │ │
│  │ 自定义范围：     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ☐ 启用          │ │  │  │              [章节详细统计图标] 章节详细统计                │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 章节范围：       │ │  │  │ 章节号 │ 章节标题      │ 字数  │ 状态      │   │ │
│  │ 从第[_]章       │ │  │  │ ──────┼──────────────┼──────┼────────── │   │ │
│  │ 到第[_]章       │ │  │  │ 第1章  │ [未设置]      │ [0]  │ [未开始]  │   │ │
│  │                 │ │  │  │ 第2章  │ [未设置]      │ [0]  │ [未开始]  │   │ │
│  │ 人物范围：       │ │  │  │ 第3章  │ [未设置]      │ [0]  │ [未开始]  │   │ │
│  │ ☑ 主角          │ │  │  │ ...    │ ...          │ ...  │ ...       │   │ │
│  │ ☑ 重要角色       │ │  │  │                                             │   │ │
│  │ ☑ 配角          │ │  │  │ [刷新统计] [导出统计] [详细报告]              │   │ │
│  │ ☐ 龙套          │ │  │  └─────────────────────────────────────────────┘   │ │
│  └─────────────────┘ │  │                                                     │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [图表设置图标] 图表设置     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 图表类型：       │ │  │  │                                             │   │ │
│  │ ○ 柱状图        │ │  │  │                                             │   │ │
│  │ ○ 折线图        │ │  │  │                                             │   │ │
│  │ ○ 饼图          │ │  │  │                                             │   │ │
│  │ ○ 面积图        │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │                                                     │ │
│  │ 显示设置：       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ☑ 显示数值       │ │  │  │                 📅 写作日历                  │   │ │
│  │ ☑ 显示趋势       │ │  │  │                                             │   │ │
│  │ ☑ 显示平均线     │ │  │  │    [暂无写作记录]                            │   │ │
│  │ ☐ 显示预测       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │    请开始创作以查看日历                       │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  📤 导出设置     │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 导出格式：       │ │  │                                                     │ │
│  │ ☑ PDF报告       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ☑ Excel表格     │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  │ ☑ 图片文件       │ │  │  │                                             │   │ │
│  │ ☑ JSON数据      │ │  │  │  [刷新数据] [导出报告] [打印统计]            │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [导出统计]       │ │  │  │  [设置提醒] [数据备份] [历史对比]            │   │ │
│  │ [定时报告]       │ │  │  │                                             │   │ │
│  │ [邮件发送]       │ │  │  │  [自定义报表] [分享统计] [数据分析]          │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.10 AI聊天界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      [AI聊天图标] AI聊天 - 智能写作助手对话                     │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧聊天配置区    │                    右侧对话显示区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [AI模型配置图标] AI模型配置   │ │  │                                                     │ │
│  │                 │ │  │                   [对话历史图标] 对话历史区域                   │ │
│  │ 当前模型：       │ │  │                                                     │ │
│  │ [选择模型 ▼]    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 连接状态：       │ │  │  │            [对话历史为空]                   │   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │        开始与AI助手对话吧！                  │   │ │
│  │ 模型参数：       │ │  │  │                                             │   │ │
│  │ 温度：[___]      │ │  │  │                                             │   │ │
│  │ 最大长度：[____] │ │  │  │                                             │   │ │
│  │ 上下文：[____]   │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [对话历史图标] 对话历史     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 历史会话：       │ │  │  │                                             │   │ │
│  │ [暂无历史记录]   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 会话操作：       │ │  │  │                                             │   │ │
│  │ [新建对话]       │ │  │  │                                             │   │ │
│  │ [保存对话]       │ │  │  │                                             │   │ │
│  │ [删除对话]       │ │  │  │                                             │   │ │
│  │ [导出对话]       │ │  │  │                                             │   │ │
│  │ [清空历史]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [快速提问图标] 快速提问     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 问题分类：       │ │  │  │                                             │   │ │
│  │ [选择分类 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 常用问题：       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [添加模板]       │ │  │  │                                             │   │ │
│  │ [编辑模板]       │ │  │  │                                             │   │ │
│  │ [导入模板]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │  [对话设置图标] 对话设置     │ │  │  │                 [输入区域图标] 输入区域                  │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 角色扮演：       │ │  │  │  ┌─────────────────────────────────────┐   │   │ │
│  │ ☐ 启用          │ │  │  │  │                                     │   │   │ │
│  │                 │ │  │  │  │        请输入您的问题...             │   │   │ │
│  │ 扮演角色：       │ │  │  │  │                                     │   │   │ │
│  │ [选择角色 ▼]    │ │  │  │  │                                     │   │   │ │
│  │                 │ │  │  │  │                                     │   │   │ │
│  │ 上下文记忆：     │ │  │  │  │                                     │   │   │ │
│  │ ☐ 启用          │ │  │  │  │                                     │   │   │ │
│  │                 │ │  │  │  └─────────────────────────────────────┘   │   │ │
│  │ 记忆长度：       │ │  │  │                                             │   │ │
│  │ [选择长度 ▼]    │ │  │  │  [发送消息] [清空输入] [插入模板] [语音输入]  │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 智能建议：       │ │  │                                                     │ │
│  │ ☐ 启用          │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [对话统计图标] 对话统计                    │   │ │
│  │ [重置对话]       │ │  │  │                                             │   │ │
│  │ [导出设置]       │ │  │  │  当前会话：[0] 条消息                        │   │ │
│  └─────────────────┘ │  │  │  字符统计：[0] / [上限]                      │   │ │
│                     │ │  │  │  响应时间：[0] 秒                            │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [查看详情] [使用统计] [反馈评价]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.11 提示词库界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [提示词库图标] 提示词库 - 智能提示词管理系统                  │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧分类管理区    │                    右侧提示词编辑区                      │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [分类树图标] 分类树结构   │ │  │                                                     │ │
│  │                 │ │  │              [提示词编辑器图标] 提示词编辑器                        │ │
│  │ 搜索提示词：     │ │  │                                                     │ │
│  │ [____________]  │ │  │  当前提示词：[未选择提示词]                         │ │
│  │                 │ │  │                                                     │ │
│  │ 分类结构：       │ │  │  [基本信息] [提示词内容] [使用说明] [版本历史]       │ │
│  │ ├ [大纲相关图标] 大纲相关    │ │  │                                                     │ │
│  │ │  [暂无提示词]  │ │  ├─────────────────────────────────────────────────────┤ │
│  │ ├ [章节相关图标] 章节相关    │ │  │                                                     │ │
│  │ │  [暂无提示词]  │ │  │                   [基本信息图标] 基本信息                       │ │
│  │ ├ [人物相关图标] 人物相关    │ │  │                                                     │ │
│  │ │  [暂无提示词]  │ │  │  提示词名称：                                       │ │
│  │ ├ [世界观图标] 世界观      │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ │  [暂无提示词]  │ │  │  │                                             │   │ │
│  │ ├ [写作技巧图标] 写作技巧     │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ │  [暂无提示词]  │ │  │                                                     │ │
│  │ └ [自定义图标] 自定义      │ │  │  提示词描述：                                       │ │
│  │    [暂无提示词]  │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  分类：                                             │ │
│  │                 │ │  │  [选择分类 ▼]                                       │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  标签：                                             │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  适用模型：                                         │ │
│  │                 │ │  │  ☐ GPT  ☐ Claude  ☐ Gemini  ☐ 其他               │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  质量评级：☆☆☆☆☆                                  │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  使用频率：[未使用]                                 │ │
│  │                 │ │  │  创建时间：[未创建]                                 │ │
│  │                 │ │  │  最后修改：[未修改]                                 │ │
│  │ 分类操作：       │ │  │                                                     │ │
│  │ [新建分类]       │ │  │  提示词内容：                                       │ │
│  │ [导入模板]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [导出模板]       │ │  │  │                                             │   │ │
│  │ [分类管理]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [提示词列表图标] 提示词列表   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 排序方式：       │ │  │  │                                             │   │ │
│  │ [选择排序 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 筛选条件：       │ │  │  │                                             │   │ │
│  │ ☑ 内置模板       │ │  │  │                                             │   │ │
│  │ ☑ 自定义模板     │ │  │  │                                             │   │ │
│  │ ☑ 收藏模板       │ │  │  │                                             │   │ │
│  │ ☐ 共享模板       │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 当前选中：       │ │  │  │                                             │   │ │
│  │ [未选择]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 提示词操作：     │ │  │  │                                             │   │ │
│  │ [新建提示词]     │ │  │  │                                             │   │ │
│  │ [复制提示词]     │ │  │  │                                             │   │ │
│  │ [删除提示词]     │ │  │  │                                             │   │ │
│  │ [批量操作]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [模板统计图标] 模板统计     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 模板统计：       │ │  │  │                                             │   │ │
│  │ 内置：[0]个      │ │  │  │                                             │   │ │
│  │ 自定义：[0]个    │ │  │  │                                             │   │ │
│  │ 收藏：[0]个      │ │  │  │                                             │   │ │
│  │ 共享：[0]个      │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 管理操作：       │ │  │  │                                             │   │ │
│  │ [批量导入]       │ │  │  │                                             │   │ │
│  │ [批量导出]       │ │  │  │                                             │   │ │
│  │ [模板备份]       │ │  │  │                                             │   │ │
│  │ [恢复备份]       │ │  │  │                                             │   │ │
│  │ [清理无效]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│                     │ │  │  ┌─────────────────────────────────────────────┐   │ │
│                     │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [保存提示词] [测试提示词] [应用到项目]      │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [复制内容] [导出提示词] [分享模板]          │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [版本历史] [使用统计] [质量评估]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.12 上下文管理界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                  [上下文管理图标] 上下文管理 - 智能上下文管理系统                │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧分类管理区    │                    右侧上下文内容区                      │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [分类管理图标] 分类管理     │ │  │                                                     │ │
│  │                 │ │  │              [上下文编辑器图标] 上下文编辑器                        │ │
│  │ 上下文类型：     │ │  │                                                     │ │
│  │ ├ [项目级图标] 项目级      │ │  │  当前上下文：[未选择上下文]                         │ │
│  │ │  [暂无内容]    │ │  │                                                     │ │
│  │ ├ [章节级图标] 章节级      │ │  │  上下文名称：                                       │ │
│  │ │  [暂无内容]    │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ ├ [人物级图标] 人物级      │ │  │  │                                             │   │ │
│  │ │  [暂无内容]    │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ ├ [场景级图标] 场景级      │ │  │                                                     │ │
│  │ │  [暂无内容]    │ │  │  类型：[选择类型 ▼]                                 │ │
│  │ └ [自定义图标] 自定义      │ │  │                                                     │ │
│  │    [暂无内容]    │ │  │  优先级：☆☆☆☆☆                                    │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  关联章节：                                         │ │
│  │                 │ │  │  [选择章节 ▼]                                       │ │
│  │                 │ │  │                                                     │ │
│  │                 │ │  │  标签：                                             │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │                                                     │ │
│  │ 分类操作：       │ │  │  上下文内容：                                       │ │
│  │ [新建分类]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [管理分类]       │ │  │  │                                             │   │ │
│  │ [导入分类]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [上下文列表图标] 上下文列表   │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 搜索上下文：     │ │  │  │                                             │   │ │
│  │ [____________]  │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 筛选条件：       │ │  │  │                                             │   │ │
│  │ [选择类型 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 排序方式：       │ │  │  │                                             │   │ │
│  │ [选择排序 ▼]    │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 当前列表：       │ │  │  │                                             │   │ │
│  │ [暂无上下文]     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 上下文操作：     │ │  │  │                                             │   │ │
│  │ [新建上下文]     │ │  │  │                                             │   │ │
│  │ [批量导入]       │ │  │  │                                             │   │ │
│  │ [批量操作]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │  [智能功能图标] 智能功能     │ │  │  │              [使用说明图标] 使用说明                    │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ AI辅助：         │ │  │  │  上下文用于为AI提供背景信息，提高生成质量    │   │ │
│  │ [自动提取]       │ │  │  │                                             │   │ │
│  │ [智能分析]       │ │  │  │  • 项目级：全局设定和规则                   │   │ │
│  │ [关联推荐]       │ │  │  │  • 章节级：章节相关信息                     │   │ │
│  │ [优化建议]       │ │  │  │  • 人物级：角色背景和关系                   │   │ │
│  │                 │ │  │  │  • 场景级：环境和氛围描述                   │   │ │
│  │ 分析状态：       │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ [等待操作]       │ │  │                                                     │ │
│  └─────────────────┘ │  │  ┌─────────────────────────────────────────────┐   │ │
│                     │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [配置设置图标] 配置设置     │ │  │  │  [保存上下文] [删除上下文] [复制内容]        │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 自动保存：       │ │  │  │  [导出上下文] [版本历史] [使用统计]          │   │ │
│  │ ☐ 启用          │ │  │  │                                             │   │ │
│  │                 │ │  │  │  [智能优化] [关联分析] [批量管理]            │   │ │
│  │ 版本控制：       │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ ☐ 启用          │ │  │                                                     │ │
│  │                 │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ 最大版本：       │ │  │  │              [上下文统计图标] 上下文统计                  │   │ │
│  │ [___] 个        │ │  │  │                                             │   │ │
│  │                 │ │  │  │  创建时间：[未创建]                         │   │ │
│  │ [重置设置]       │ │  │  │  最后修改：[未修改]                         │   │ │
│  │ [导出配置]       │ │  │  │  使用次数：[0] 次                           │   │ │
│  └─────────────────┘ │  │  │  字符统计：[0] 字符                         │   │ │
│                     │ │  │  │                                             │   │ │
│                     │ │  │  │  [详细统计] [使用记录] [效果分析]            │   │ │
│                     │ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  └─────────────────────────────────────────────────────┘ │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.13 向量库检索界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    [向量检索图标] 向量库检索 - 智能语义检索系统                  │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧检索配置区    │                    右侧检索结果区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [嵌入模型图标] 嵌入模型配置 │ │  │                                                     │ │
│  │                 │ │  │              [检索结果图标] 检索结果展示                        │ │
│  │ 嵌入模型：       │ │  │                                                     │ │
│  │ [选择模型 ▼]    │ │  │  当前查询：[未输入查询]                             │ │
│  │                 │ │  │                                                     │ │
│  │ 连接状态：       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [未连接]         │ │  │  │                                             │   │ │
│  │                 │ │  │  │            [检索结果为空]                   │   │ │
│  │ API配置：        │ │  │  │                                             │   │ │
│  │ [配置API密钥]    │ │  │  │         请输入查询内容开始检索               │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ [测试连接]       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [检索参数图标] 检索参数     │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 检索数量：       │ │  │  │                                             │   │ │
│  │ [___] 条        │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 相似度阈值：     │ │  │  │                                             │   │ │
│  │ [___] (0-100%)  │ │  │  │                                             │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 检索范围：       │ │  │  │                                             │   │ │
│  │ ☐ 章节内容       │ │  │  │                                             │   │ │
│  │ ☐ 人物设定       │ │  │  │                                             │   │ │
│  │ ☐ 大纲信息       │ │  │  │                                             │   │ │
│  │ ☐ 世界观设定     │ │  │  │                                             │   │ │
│  │ ☐ 对话记录       │ │  │  │                                             │   │ │
│  │ ☐ 提示词库       │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │  [向量库管理图标] 向量库管理   │ │  │  │              [查询输入图标] 查询输入区                   │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │ 向量库状态：     │ │  │  │  ┌─────────────────────────────────────┐   │   │ │
│  │ [未建立]         │ │  │  │  │                                     │   │   │ │
│  │                 │ │  │  │  │        请输入检索查询...             │   │   │ │
│  │ 文档数量：[0]    │ │  │  │  │                                     │   │   │ │
│  │ 向量维度：[0]    │ │  │  │  │                                     │   │   │ │
│  │                 │ │  │  │  └─────────────────────────────────────┘   │   │ │
│  │ 库管理操作：     │ │  │  │                                             │   │ │
│  │ [建立索引]       │ │  │  │  [开始检索] [清空输入] [保存查询]            │   │ │
│  │ [重建索引]       │ │  │  │                                             │   │ │
│  │ [清空向量库]     │ │  │  │  [语音输入] [模板插入] [历史查询]            │   │ │
│  │ [导出向量库]     │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ [导入向量库]     │ │  │                                                     │ │
│  └─────────────────┘ │  │  ┌─────────────────────────────────────────────┐   │ │
│                     │ │  │  │              [检索统计图标] 检索统计                    │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [检索历史图标] 检索历史     │ │  │  │  检索状态：[等待输入]                       │   │ │
│  │                 │ │  │  │  响应时间：[0] 秒                           │   │ │
│  │ 历史记录：       │ │  │  │  结果数量：[0] 条                           │   │ │
│  │ [暂无历史记录]   │ │  │  │  相似度范围：[0-0]                          │   │ │
│  │                 │ │  │  │                                             │   │ │
│  │                 │ │  │  │  [详细统计] [性能分析] [使用报告]            │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  └─────────────────────────────────────────────────────┘ │
│  │                 │ │                                                         │
│  │ 历史操作：       │ │                                                         │
│  │ [清空历史]       │ │                                                         │
│  │ [导出历史]       │ │                                                         │
│  │ [导入历史]       │ │                                                         │
│  └─────────────────┘ │                                                         │
│                     │                                                         │
└─────────────────────┴───────────────────────────────────────────────────────────┘
```

### 6.14 设置界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        [设置图标] 设置 - 系统配置管理中心                       │
├─────────────────────┬───────────────────────────────────────────────────────────┤
│                     │                                                         │
│    左侧设置导航区    │                    右侧设置内容区                        │
│      (40%)          │                      (60%)                             │
│                     │                                                         │
│  ┌─────────────────┐ │  ┌─────────────────────────────────────────────────────┐ │
│  │  [设置搜索图标] 设置搜索     │ │  │                                                     │ │
│  │                 │ │  │              [API设置图标] API设置                             │ │
│  │ 搜索设置项：     │ │  │                                                     │ │
│  │ [____________]  │ │  │  当前设置：[API配置]                                │ │
│  │                 │ │  │                                                     │ │
│  │ [搜索] [清空]    │ │  │  [OpenAI] [Claude] [Gemini] [ModelScope] [其他]     │ │
│  └─────────────────┘ │  │                                                     │ │
│                     │ │  ├─────────────────────────────────────────────────────┤ │
│  ┌─────────────────┐ │  │                                                     │ │
│  │  [设置分类图标] 设置分类     │ │  │                   [AI模型设置图标] AI模型设置                     │ │
│  │                 │ │  │                                                     │ │
│  │ [基础设置图标] 基础设置       │ │  │  API密钥：                                          │ │
│  │ [界面设置图标] 界面设置       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [编辑器图标] 编辑器设置       │ │  │  │                                             │   │ │
│  │ [AI模型图标] AI模型设置       │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ [数据管理图标] 数据管理       │ │  │                                                     │ │
│  │ [快捷键图标] 快捷键设置       │ │  │  模型名称：                                         │ │
│  │ [高级设置图标] 高级设置       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │ [输入任意模型名称，如gpt-4o, claude-3-opus] │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │                 │ │  │  [提示图标] 支持该提供商的所有模型，无限制                   │ │
│  │ [关于帮助图标] 关于和帮助     │ │  │                                                     │ │
│  │                 │ │  │  API地址：                                          │ │
│  │ 当前选中：       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ [AI模型设置]     │ │  │  │                                             │   │ │
│  └─────────────────┘ │  │  └─────────────────────────────────────────────┘   │ │
│                     │ │  │                                                     │ │
│  ┌─────────────────┐ │  │  连接超时：[___]秒                                  │ │
│  │  [设置管理图标] 设置管理     │ │  │                                                     │ │
│  │                 │ │  │  请求频率限制：[___]次/分钟                         │ │
│  │ 配置操作：       │ │  │                                                     │ │
│  │ [导入设置]       │ │  │  [测试连接] [保存配置] [重置配置]                   │ │
│  │ [导出设置]       │ │  │                                                     │ │
│  │ [重置所有]       │ │  │  连接状态：[未连接]                                 │ │
│  │ [备份设置]       │ │  │                                                     │ │
│  │ [恢复备份]       │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │                 │ │  │  │              [自定义模型图标] 自定义模型管理               │   │ │
│  │ 设置状态：       │ │  │  │                                             │   │ │
│  │ [正常]           │ │  │  │  模型列表：[暂无自定义模型]                  │   │ │
│  └─────────────────┘ │  │  │                                             │   │ │
│                     │ │  │  │                                             │   │ │
│  ┌─────────────────┐ │  │  │                                             │   │ │
│  │  [系统信息图标] 系统信息      │ │  │  │  [添加模型] [编辑模型] [删除模型]            │   │ │
│  │                 │ │  │  │  [导入模型] [导出模型] [批量管理]            │   │ │
│  │ [未知]           │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 系统：           │ │  │                                                     │ │
│  │ [未知]           │ │  │  ┌─────────────────────────────────────────────┐   │ │
│  │ 内存使用：       │ │  │  │              [操作面板图标] 操作面板                    │   │ │
│  │ [未知]           │ │  │  │                                             │   │ │
│  │ 存储空间：       │ │  │  │  [应用设置] [取消更改] [恢复默认]            │   │ │
│  │ [未知]           │ │  │  │                                             │   │ │
│  │                 │ │  │  │  [导出配置] [导入配置] [重置所有]            │   │ │
│  │ 最后更新：       │ │  │  │                                             │   │ │
│  │ [未知]           │ │  │  │  [检查更新] [查看帮助] [关于软件]            │   │ │
│  │                 │ │  │  └─────────────────────────────────────────────┘   │ │
│  │ 系统操作：       │ │  └─────────────────────────────────────────────────────┘ │
│  │ [检查更新]       │                                                         │
│  │ [查看日志]       │                                                         │
│  │ [重启应用]       │                                                         │
│  │ [清理缓存]       │                                                         │
│  └─────────────────┘                                                         │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. 功能详细说明

### 7.1 大纲生成功能

#### 7.1.1 功能概述
大纲生成是AI小说助手的核心功能之一，通过AI模型自动生成完整的小说大纲结构，包括故事梗概、章节安排、人物设定和世界观构建。

#### 7.1.2 主要特性
- **全面模型支持**：支持OpenAI全系列（GPT-4o、GPT-4、GPT-3.5等）、Claude全系列（Claude-3、Claude-2等）、Gemini全系列（Gemini-2.0、Gemini-1.5等）、ModelScope平台所有模型、SiliconFlow平台所有模型、Ollama生态系统所有模型，以及任何OpenAI兼容API，用户可自由选择和配置任意模型
- **模板化生成**：内置多种大纲模板，支持用户自定义模板
- **参数化配置**：可设置章节数、字数、人物数量等详细参数
- **分段生成**：支持按章节范围分段生成，便于大型项目管理
- **JSON格式输出**：结构化输出，便于后续编辑和处理
- **网络小说适配**：针对番茄小说、飞卢、17K、起点、纵横、晋江、七猫等平台读者优化

#### 7.1.3 内置选项配置

**1. 小说类型选项**
系统内置丰富的小说类型选项，涵盖主流网络小说分类：

- **玄幻类**：玄幻奇幻、修真仙侠、异世大陆、东方玄幻、西方奇幻
- **都市类**：都市生活、都市异能、都市重生、商战职场、娱乐明星
- **历史类**：架空历史、穿越重生、历史传记、古代言情、宫廷争斗
- **科幻类**：未来科幻、星际战争、机甲文明、末世求生、时空穿越
- **武侠类**：传统武侠、新派武侠、古典仙侠、现代修真、武侠奇幻
- **言情类**：现代言情、古代言情、穿越言情、重生复仇、豪门总裁
- **军事类**：军事战争、抗战烽火、现代军事、特种兵王、谍战风云
- **游戏类**：虚拟网游、电竞竞技、游戏异界、数据流、全息网游
- **悬疑类**：推理悬疑、恐怖惊悚、灵异超自然、犯罪侦探、心理悬疑
- **其他类**：轻小说、同人小说、无限流、快穿文、系统文

**2. 小说主题选项**
针对不同读者群体的主题偏好：

- **成长励志**：个人成长、奋斗拼搏、逆袭人生、梦想追求
- **情感情缘**：爱情故事、友情亲情、情感纠葛、情缘际遇
- **权谋争斗**：权力斗争、宫廷政治、商战竞争、江湖恩怨
- **冒险探索**：未知探险、寻宝历险、世界探索、神秘解谜
- **复仇救赎**：复仇雪恨、救赎重生、正义伸张、因果报应
- **家国情怀**：爱国情怀、民族大义、家族荣耀、社会责任
- **超能异术**：特殊能力、异能觉醒、超自然力量、神秘术法
- **科技未来**：科技发展、人工智能、基因改造、太空文明
- **传统文化**：传统武学、古典文化、民俗传说、历史传承
- **现实关怀**：社会现实、人性探讨、道德伦理、生活哲理

**3. 小说风格选项**
适应不同平台和读者的阅读习惯：

- **轻松幽默**：诙谐搞笑、轻松愉快、日常温馨、治愈系
- **热血激昂**：激情澎湃、热血沸腾、斗志昂扬、正能量
- **深沉厚重**：深度思考、厚重历史、哲理思辨、人文关怀
- **紧张刺激**：节奏紧凑、悬念迭起、刺激惊险、扣人心弦
- **唯美浪漫**：文笔优美、浪漫温馨、情感细腻、意境深远
- **现实写实**：贴近生活、真实描写、社会写实、平实朴素
- **奇幻瑰丽**：想象丰富、奇幻绚烂、瑰丽多彩、天马行空
- **古典雅致**：古典韵味、文雅含蓄、诗意盎然、传统美感
- **现代时尚**：时尚前卫、现代感强、潮流元素、都市气息
- **网文爽文**：爽点密集、节奏明快、代入感强、读者导向

**4. 参数限制配置**
严格按照开发计划要求设置参数限制：

- **章节数设置**：
  - 默认值：1章
  - 最小值：1章
  - 最大值：9999章
  - 输入验证：只允许正整数输入

- **每章字数设置**：
  - 默认值：3500字
  - 最小值：300字
  - 最大值：9999字
  - 输入验证：只允许正整数输入

- **人物数量设置**：
  - 主角数量：1-10个（默认1个）
  - 重要角色数量：0-50个（默认3个）
  - 配角数量：0-100个（默认5个）
  - 龙套数量：0-200个（默认10个）

**5. 平台适配优化**
针对不同网络小说平台的特点进行优化：

- **起点中文网**：注重世界观构建、等级体系、升级爽点
- **番茄小说**：快节奏、短章节、密集爽点、简洁文风
- **飞卢小说网**：同人改编、二次元元素、轻松幽默
- **17K小说网**：多元化题材、创新设定、个性化角色
- **纵横中文网**：传统网文、经典套路、稳定更新
- **晋江文学城**：耽美言情、细腻情感、文笔优美
- **七猫免费小说**：短篇快读、碎片化阅读、即时满足

#### 7.1.4 生成范围控制
支持灵活的章节范围生成：

- **起始章节**：用户可指定从第几章开始生成（默认第1章）
- **结束章节**：用户可指定生成到第几章（默认第10章）
- **范围验证**：确保起始章节不大于结束章节
- **连续性保证**：与已有大纲保持逻辑连贯性
- **增量生成**：支持在现有大纲基础上扩展新章节

#### 7.1.3 技术实现
```python
class OutlineGenerator:
    def __init__(self, ai_service):
        self.ai_service = ai_service
        self.template_manager = TemplateManager()

    async def generate_outline(self, config: OutlineConfig):
        # 构建提示词
        prompt = self.build_prompt(config)

        # 调用AI模型
        response = await self.ai_service.generate(prompt)

        # 解析JSON结果
        outline = self.parse_outline(response)

        return outline
```

### 7.2 大纲编辑功能

#### 7.2.1 功能概述
大纲编辑功能提供可视化的大纲编辑界面，支持对生成的大纲进行精细化调整和完善，包括基本信息修改、故事梗概优化、世界观扩展等。

#### 7.2.2 主要特性
- **分标签页编辑**：基本信息、故事梗概、世界观、章节大纲分别编辑
- **AI辅助优化**：支持AI辅助优化标题、主题、梗概等内容
- **版本管理**：支持大纲版本保存和回滚
- **实时预览**：编辑过程中实时预览效果
- **导入导出**：支持大纲的导入导出功能

#### 7.2.3 编辑模式
- **手动编辑**：用户直接编辑文本内容
- **AI辅助编辑**：选择文本后使用AI进行优化
- **模板应用**：应用预设模板快速完善内容
- **批量操作**：支持批量修改章节信息

### 7.3 章节编辑功能

#### 7.3.1 功能概述
章节编辑功能提供专业的文本编辑环境，支持章节内容的创作、修改和管理，集成AI辅助工具提升写作效率。

#### 7.3.2 主要特性
- **章节列表管理**：树状结构显示所有章节，支持拖拽排序
- **富文本编辑器**：支持格式化文本编辑，语法高亮
- **AI辅助工具**：标题生成、内容扩展、逻辑检查等
- **模板系统**：开头模板、过渡模板、结尾模板等
- **实时统计**：字数统计、阅读时间估算等

#### 7.3.3 编辑器特性
- **语法高亮**：对话、描述、动作等不同内容类型高亮显示
- **自动保存**：定时自动保存，防止内容丢失
- **专注模式**：隐藏干扰元素，专注写作
- **快捷操作**：丰富的快捷键支持

### 7.4 章节生成功能

#### 7.4.1 功能概述
章节生成功能基于大纲和上下文信息，使用AI模型自动生成章节内容，支持多种生成模式和智能上下文管理。

#### 7.4.2 生成模式
- **完整生成**：根据章节大纲生成完整章节内容
- **续写模式**：基于已有内容进行续写
- **分段生成**：分段生成长章节，保持连贯性
- **对话生成**：专门生成对话内容
- **描述生成**：专门生成场景描述

#### 7.4.3 上下文管理
- **智能上下文提取**：自动提取相关的前文内容
- **角色状态跟踪**：跟踪角色在不同章节的状态变化
- **伏笔线索管理**：确保伏笔的合理埋设和回收
- **世界观一致性**：保持世界观设定的一致性

#### 7.4.4 质量控制
- **长度控制**：精确控制生成内容的长度
- **风格一致性**：保持与已有内容的风格一致
- **逻辑连贯性**：确保情节发展的逻辑合理
- **降AI味处理**：减少AI生成内容的机械化特征

### 7.5 章节分析功能

#### 7.5.1 功能概述
章节分析功能使用AI模型对已完成的章节进行深度分析，提供多维度的分析报告和改进建议。

#### 7.5.2 分析维度
- **核心剧情分析**：分析章节的主要情节线和发展脉络
- **故事梗概提取**：提取章节的核心内容和关键信息
- **优缺点分析**：识别章节的优点和需要改进的地方
- **角色标注**：标注出现的角色及其作用
- **物品标注**：标注重要物品和道具
- **改进建议**：提供具体的改进建议和修改方向

#### 7.5.3 分析报告
- **结构化报告**：以结构化格式呈现分析结果
- **可视化展示**：使用图表展示分析数据
- **对比分析**：支持多个章节的对比分析
- **历史记录**：保存分析历史，跟踪改进过程

### 7.6 人物编辑功能

#### 7.6.1 功能概述
人物编辑功能提供完整的角色管理系统，支持角色的创建、编辑、分类管理和AI辅助生成。

#### 7.6.2 角色信息管理
- **基本信息**：姓名、性别、年龄、职业等基础信息
- **外貌特征**：身高、体重、外貌描述、特殊标记等
- **性格特点**：性格描述、行为习惯、说话方式等
- **背景经历**：出身背景、重要经历、成长轨迹等
- **能力技能**：特殊能力、技能等级、武器装备等
- **关系网络**：与其他角色的关系和互动历史

#### 7.6.3 AI辅助功能
- **角色生成**：基于设定自动生成完整角色
- **设定完善**：AI辅助完善角色设定
- **背景生成**：生成详细的角色背景故事
- **对话模拟**：模拟角色对话，测试角色一致性

### 7.7 人物关系图功能

#### 7.7.1 功能概述
人物关系图功能提供可视化的角色关系展示，支持关系的创建、编辑和分析，帮助作者理清复杂的人物关系网络。

#### 7.7.2 关系类型
- **亲情关系**：父子、母女、兄弟姐妹等血缘关系
- **爱情关系**：恋人、夫妻、暗恋等情感关系
- **友情关系**：朋友、知己、战友等友谊关系
- **师徒关系**：师父徒弟、老师学生等传承关系
- **敌对关系**：仇人、对手、竞争者等对立关系
- **合作关系**：盟友、合作伙伴、同事等协作关系

#### 7.7.3 可视化特性
- **力导向布局**：自动计算最优的节点布局
- **关系强度显示**：通过线条粗细表示关系强度
- **节点大小调节**：根据角色重要性调整节点大小
- **颜色编码**：不同关系类型使用不同颜色
- **交互操作**：支持拖拽、缩放、点击查看详情

### 7.8 统计信息功能

#### 7.8.1 功能概述
统计信息功能提供全面的创作数据统计和分析，帮助作者了解创作进度、质量指标和写作习惯。

#### 7.8.2 统计维度
- **基础统计**：总字数、章节数、完成度等基本指标
- **章节统计**：各章节字数、完成状态、质量评分
- **人物统计**：角色出场频率、重要程度分析
- **进度统计**：创作进度跟踪、目标完成情况
- **时间统计**：写作时间分布、效率分析

#### 7.8.3 可视化图表
- **趋势图**：显示创作进度的时间趋势
- **柱状图**：对比不同章节的字数分布
- **饼图**：显示不同类型内容的占比
- **日历图**：显示每日创作活动情况
- **雷达图**：多维度质量评估

### 7.9 AI聊天功能

#### 7.9.1 功能概述
AI聊天功能提供与AI模型的实时对话交流，用于验证模型可用性、获取写作建议和解决创作问题。

#### 7.9.2 对话模式
- **自由对话**：与AI进行开放式对话交流
- **角色扮演**：AI扮演特定角色进行对话
- **专业咨询**：针对写作技巧进行专业咨询
- **创意讨论**：讨论剧情发展和创意想法

#### 7.9.3 辅助功能
- **快速提问模板**：预设常用问题模板
- **对话历史管理**：保存和管理对话记录
- **上下文记忆**：维持对话的上下文连贯性
- **多轮对话**：支持复杂的多轮对话交互

### 7.10 提示词库功能

#### 7.10.1 功能概述
提示词库功能是AI小说助手的核心辅助工具，提供丰富的专业提示词模板库，包含内置的高质量模板和用户自定义模板系统。通过标准化的提示词模板，用户可以快速获得高质量的AI生成内容，大幅提升创作效率和内容质量。

#### 7.10.2 内置模板详细分类

**1. 大纲相关模板**
- **标准大纲生成**：生成完整的小说大纲结构
- **细纲扩展**：将粗略大纲扩展为详细细纲
- **大纲优化**：优化现有大纲的逻辑和结构
- **分章大纲**：按章节生成详细的章节大纲
- **主题深化**：深化小说主题和核心思想
- **冲突设计**：设计主要冲突和次要冲突
- **高潮设计**：设计故事高潮和转折点

**2. 章节相关模板**
- **黄金开篇**：生成吸引读者的精彩开头
- **情节推进**：推动故事情节自然发展
- **对话生成**：生成符合角色性格的对话
- **场景描写**：生成生动的场景描述
- **动作描写**：生成精彩的动作场面
- **心理描写**：深入刻画角色内心活动
- **结尾收束**：设计令人满意的章节结尾
- **过渡衔接**：自然衔接前后章节内容

**3. 人物相关模板**
- **角色设定**：创建完整的角色档案
- **人设生成**：生成独特的角色人设
- **背景故事**：创造丰富的角色背景
- **性格塑造**：深度塑造角色性格特征
- **对话风格**：为角色设定独特的说话方式
- **成长轨迹**：设计角色的成长和变化
- **关系网络**：构建角色间的复杂关系
- **反派设计**：创造有深度的反派角色

**4. 世界观相关模板**
- **背景设定**：构建小说的世界背景
- **规则体系**：建立世界的运行规则
- **文化社会**：设计社会文化体系
- **历史背景**：创造世界的历史脉络
- **地理环境**：描述世界的地理特征
- **科技水平**：设定世界的科技发展
- **政治体系**：构建政治权力结构
- **经济体系**：设计经济运行模式

**5. 写作技巧模板**
- **续写优化**：自然续写现有内容
- **扩写丰富**：扩展和丰富现有内容
- **润色美化**：提升文字的表达质量
- **改写重构**：重新组织和表达内容
- **降AI味处理**：减少AI生成内容的机械感
- **风格统一**：保持全文风格的一致性
- **节奏控制**：调整故事节奏的快慢
- **张力营造**：增强故事的紧张感

**6. 特殊功能模板**
- **金手指生成**：为主角设计特殊能力
- **审稿检查**：检查内容的质量和问题
- **仿写练习**：模仿特定作家的写作风格
- **短篇创作**：专门用于短篇小说创作
- **网文优化**：针对网络小说的特殊优化
- **类型适配**：适配不同小说类型的特点
- **读者导向**：针对特定读者群体优化

#### 7.10.3 模板结构设计

**标准模板格式：**
```
模板名称：[模板的简洁名称]
模板描述：[模板功能的详细说明]
模板分类：[所属的功能分类]
适用场景：[适合使用的具体场景]
模板内容：[具体的提示词内容]
变量说明：[模板中变量的说明]
使用示例：[模板的使用示例]
注意事项：[使用时需要注意的事项]
```

**变量系统：**
- **基础变量**：[用户输入的标题]、[用户输入的类型]、[用户输入的主题]
- **数值变量**：[用户设置的章节数]、[用户设置的字数]、[目标字数]
- **选择变量**：[用户选择的风格]、[用户选择的类型]、[用户选择的角色]
- **动态变量**：[当前章节]、[前一章节]、[角色列表]、[已有内容]

#### 7.10.4 内置提示词模板示例

**1. 标准大纲生成模板**
```
模板名称：标准大纲提示词
模板描述：生成完整的小说大纲，包括人物设定、故事梗概、章节结构等
模板分类：大纲相关
适用场景：新项目创建时的大纲生成

模板内容：
请为我创建一部小说的详细大纲，具体要求如下：

基本信息：
- 小说标题：[用户输入的标题]
- 小说类型：[用户输入的类型]
- 主题：[用户输入的主题]
- 风格：[用户输入的风格]

结构设置：
- 章节数：[用户设置的章节数]章
- 每章字数：[用户设置的字数]字
- 生成范围：从第[起始章]章到第[结束章]章

人物设置：
- 主角数量：[用户设置的主角数量]个
- 重要角色数量：[用户设置的重要角色数量]个
- 配角数量：[用户设置的配角数量]个
- 龙套数量：[用户设置的龙套数量]个

请生成以下内容：
1. 小说标题（如果用户未提供，请创建一个吸引人的标题）
2. 核心主题（深入阐述小说要表达的主题思想）
3. 主要人物（包括姓名、身份、性格特点、背景故事、在故事中的作用）
4. 故事梗概（完整的故事概要，包括开端、发展、高潮、结局）
5. 章节结构（每章包含章节号、章节标题、章节简介、主要情节、涉及人物）
6. 世界观设定（故事发生的世界背景、规则、文化等）

特别要求：
1. 章节标题必须包含章节号，格式为"第X章：章节标题"
2. 只生成指定范围内的章节，但要保持与整体大纲的一致性
3. 人物设定要有深度，避免脸谱化
4. 情节发展要有逻辑性，冲突要合理
5. 适合[用户输入的类型]类型小说的特点
6. 符合现代网络小说读者的阅读习惯

请确保大纲结构完整、逻辑合理，并以JSON格式返回。
```

**2. 黄金开篇模板**
```
模板名称：黄金开篇生成
模板描述：生成吸引读者的精彩小说开头
模板分类：章节相关
适用场景：小说第一章或新章节的开头创作

模板内容：
请为我创作一个精彩的小说开头，要求如下：

基本信息：
- 小说类型：[用户输入的类型]
- 主角姓名：[主角姓名]
- 开篇场景：[开篇场景描述]
- 目标字数：[目标字数]字

开篇要求：
1. 立即抓住读者注意力，避免平淡的日常描述
2. 快速建立主角形象，展现其特点或困境
3. 暗示故事的主要冲突或悬念
4. 语言生动有力，节奏紧凑
5. 符合[用户输入的类型]类型的特点
6. 为后续情节发展做好铺垫

开篇技巧：
- 可以使用对话开头、动作开头、悬念开头等技巧
- 避免大段背景介绍和世界观说明
- 注重细节描写，营造画面感
- 体现主角的个性和特色
- 设置钩子，让读者想要继续阅读

请创作一个符合要求的精彩开头。
```

#### 7.10.5 模板管理系统

**1. 分类管理**
- **树状结构**：采用多级分类树状结构组织模板
- **标签系统**：支持多标签分类，便于交叉查找
- **搜索功能**：支持按名称、描述、标签搜索模板
- **收藏功能**：用户可收藏常用模板，快速访问

**2. 模板编辑器**
- **可视化编辑**：提供友好的模板编辑界面
- **语法高亮**：对变量和特殊语法进行高亮显示
- **实时预览**：编辑时实时预览模板效果
- **变量提示**：智能提示可用的变量类型

**3. 质量控制**
- **模板验证**：检查模板语法和变量的正确性
- **质量评级**：根据使用效果对模板进行评级
- **使用统计**：统计模板的使用频率和效果
- **用户反馈**：收集用户对模板质量的反馈

**4. 导入导出功能**
- **批量导入**：支持从文件批量导入模板
- **格式支持**：支持JSON、XML、TXT等格式
- **模板分享**：用户可分享自己创建的优质模板
- **在线模板库**：连接在线模板库，获取更多模板

### 7.11 上下文管理功能

#### 7.11.1 功能概述
上下文管理功能是AI小说助手的核心智能系统，专门解决AI生成内容连贯性和一致性的问题。通过智能化的上下文信息提取、管理和应用，确保AI模型能够准确理解和延续故事背景、人物特征和情节发展，大幅提升生成内容的质量和连贯性。

#### 7.11.2 上下文类型与结构

**1. 项目级上下文**
- **项目设定**：小说的基本设定和背景信息
  - 小说标题、类型、风格
  - 创作目标和受众定位
  - 整体创作规划
- **全局规则**：贯穿全书的规则和限制
  - 世界观法则和规则体系
  - 魔法/科技系统规则
  - 社会结构和文化背景
- **写作风格**：全书统一的写作风格指南
  - 叙事视角和语言风格
  - 描写偏好和表达方式
  - 特殊词汇和表达习惯

**2. 章节级上下文**
- **前章回顾**：前序章节的关键信息
  - 上一章的主要情节发展
  - 关键对话和决策
  - 情节转折点和悬念
- **当前章节**：当前章节的规划和要求
  - 章节目标和主题
  - 情节发展方向
  - 需要解决的冲突
- **后续预告**：后续章节的规划提示
  - 情节发展方向
  - 需要埋下的伏笔
  - 即将出现的转折

**3. 人物级上下文**
- **角色背景**：角色的详细背景信息
  - 基本信息（姓名、年龄、性别等）
  - 外貌特征和穿着风格
  - 性格特点和行为模式
- **关系网络**：角色间的关系状态
  - 与主角的关系
  - 与其他角色的关系
  - 关系的发展变化
- **成长轨迹**：角色的成长和变化
  - 已经经历的重要事件
  - 性格和能力的变化
  - 未来发展方向

**4. 场景级上下文**
- **环境描述**：场景的物理环境
  - 地理位置和空间布局
  - 气候条件和时间设定
  - 环境特征和标志性元素
- **氛围营造**：场景的情感氛围
  - 整体氛围和情绪基调
  - 感官描述（视觉、听觉、嗅觉等）
  - 环境与情节的呼应
- **细节设定**：场景中的重要细节
  - 关键物品和道具
  - 环境中的特殊元素
  - 可能影响情节的环境因素

**5. 自定义上下文**
- **伏笔线索**：已埋下的伏笔和线索
  - 未解之谜和悬念
  - 暗示性描述和对话
  - 需要后续呼应的元素
- **情节发展**：特定情节线的发展状态
  - 主线情节进展
  - 支线情节状态
  - 冲突发展阶段
- **其他备注**：其他需要记录的信息
  - 创作灵感和想法
  - 读者反馈和建议
  - 特殊注意事项

#### 7.11.3 上下文管理系统架构

**1. 数据存储层**
```python
class ContextRepository:
    def __init__(self, db_connection):
        self.db = db_connection

    def save_context(self, context_type, context_data, project_id, priority=5):
        """保存上下文信息到数据库"""
        context_id = self.db.execute(
            """
            INSERT INTO contexts
            (project_id, context_type, content, priority, created_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """,
            (project_id, context_type, json.dumps(context_data), priority)
        )
        return context_id

    def get_contexts(self, project_id, context_type=None, limit=10):
        """获取指定类型的上下文信息"""
        query = "SELECT * FROM contexts WHERE project_id = ?"
        params = [project_id]

        if context_type:
            query += " AND context_type = ?"
            params.append(context_type)

        query += " ORDER BY priority DESC, created_at DESC LIMIT ?"
        params.append(limit)

        return self.db.fetch_all(query, params)
```

**2. 上下文提取引擎**
```python
class ContextExtractor:
    def __init__(self, nlp_processor):
        self.nlp = nlp_processor

    def extract_from_chapter(self, chapter_text, chapter_number, project_id):
        """从章节内容中提取上下文信息"""
        # 提取关键情节
        key_events = self.nlp.extract_key_events(chapter_text)

        # 提取角色信息
        character_mentions = self.nlp.extract_character_mentions(chapter_text)

        # 提取场景描述
        scene_descriptions = self.nlp.extract_scene_descriptions(chapter_text)

        # 提取伏笔线索
        plot_hints = self.nlp.extract_plot_hints(chapter_text)

        # 组织上下文数据
        context_data = {
            "chapter_number": chapter_number,
            "key_events": key_events,
            "character_mentions": character_mentions,
            "scene_descriptions": scene_descriptions,
            "plot_hints": plot_hints
        }

        return context_data
```

**3. 上下文优化引擎**
```python
class ContextOptimizer:
    def __init__(self, ai_service):
        self.ai = ai_service

    async def optimize_context(self, context_data, target_tokens=2000):
        """优化上下文内容，控制长度和提升质量"""
        # 计算当前上下文长度
        current_tokens = self.count_tokens(context_data)

        if current_tokens <= target_tokens:
            return context_data

        # 对上下文内容进行重要性排序
        ranked_items = self.rank_by_importance(context_data)

        # 根据重要性保留内容
        optimized_data = {}
        current_count = 0

        for item, score, tokens in ranked_items:
            if current_count + tokens <= target_tokens:
                optimized_data[item] = context_data[item]
                current_count += tokens
            else:
                # 对超出部分进行摘要压缩
                summary = await self.ai.summarize(context_data[item],
                                                 target_tokens - current_count)
                optimized_data[item] = summary
                break

        return optimized_data
```

**4. 上下文应用引擎**
```python
class ContextApplier:
    def __init__(self, context_repository):
        self.repo = context_repository

    def build_generation_context(self, project_id, chapter_number,
                                max_tokens=4000, weights=None):
        """构建用于AI生成的上下文"""
        # 默认权重配置
        weights = weights or {
            "project": 0.2,
            "chapter": 0.4,
            "character": 0.2,
            "scene": 0.1,
            "custom": 0.1
        }

        # 获取各类上下文
        project_contexts = self.repo.get_contexts(
            project_id, "project", int(max_tokens * weights["project"]))
        chapter_contexts = self.repo.get_contexts(
            project_id, "chapter", int(max_tokens * weights["chapter"]))
        character_contexts = self.repo.get_contexts(
            project_id, "character", int(max_tokens * weights["character"]))
        scene_contexts = self.repo.get_contexts(
            project_id, "scene", int(max_tokens * weights["scene"]))
        custom_contexts = self.repo.get_contexts(
            project_id, "custom", int(max_tokens * weights["custom"]))

        # 组合上下文
        combined_context = self.format_contexts(
            project_contexts,
            chapter_contexts,
            character_contexts,
            scene_contexts,
            custom_contexts
        )

        return combined_context
```

#### 7.11.4 智能功能详解

**1. 自动提取功能**
- **内容分析**：使用NLP技术分析文本内容
  - 关键词提取和实体识别
  - 情感分析和主题提取
  - 重要性评分和分类
- **结构化处理**：将提取的信息结构化存储
  - 按类型分类和标记
  - 建立信息间的关联关系
  - 生成结构化的上下文数据
- **增量更新**：智能更新已有上下文
  - 识别新增和变更的信息
  - 合并相关上下文内容
  - 解决冲突和矛盾信息

**2. 智能分析功能**
- **重要性评估**：评估上下文信息的重要程度
  - 基于情节影响度的评分
  - 基于角色重要性的评分
  - 基于伏笔关联度的评分
- **相关性分析**：分析上下文间的相关性
  - 情节关联分析
  - 角色关联分析
  - 时空关联分析
- **一致性检查**：检查上下文的一致性
  - 逻辑一致性检查
  - 时间线一致性检查
  - 角色设定一致性检查

**3. 关联推荐功能**
- **智能推荐**：基于当前内容推荐相关上下文
  - 情节相关推荐
  - 角色相关推荐
  - 场景相关推荐
- **上下文补充**：自动补充缺失的上下文
  - 识别上下文空缺
  - 生成补充内容
  - 整合到现有上下文
- **伏笔追踪**：追踪和关联伏笔线索
  - 识别潜在伏笔
  - 关联相关情节
  - 提示伏笔回收时机

**4. 优化建议功能**
- **内容优化**：提供上下文内容优化建议
  - 冗余信息精简
  - 关键信息强化
  - 表达方式优化
- **结构优化**：提供上下文结构优化建议
  - 层次结构调整
  - 关联关系优化
  - 优先级重排
- **应用优化**：提供上下文应用优化建议
  - 使用时机建议
  - 组合方式建议
  - 权重分配建议

#### 7.11.5 应用场景与使用方法

**1. 章节生成场景**
- **前置准备**：
  - 配置上下文类型和优先级
  - 设置上下文长度限制
  - 选择相关角色和场景
- **自动应用**：
  - 系统自动提取相关上下文
  - 优化组合生成提示词
  - 应用到AI生成过程
- **手动调整**：
  - 查看和编辑自动提取的上下文
  - 添加或移除特定上下文
  - 调整上下文权重

**2. 内容编辑场景**
- **上下文参考**：
  - 显示相关上下文作为参考
  - 提供一致性检查
  - 标记潜在冲突
- **实时建议**：
  - 基于当前编辑内容提供建议
  - 推荐相关上下文信息
  - 提示潜在伏笔和关联
- **智能辅助**：
  - 自动补全基于上下文的内容
  - 提供符合上下文的表达建议
  - 检查与上下文的一致性

**3. 伏笔管理场景**
- **伏笔记录**：
  - 标记和记录重要伏笔
  - 关联相关章节和角色
  - 设置伏笔优先级
- **伏笔追踪**：
  - 追踪伏笔使用状态
  - 提示伏笔回收时机
  - 检查伏笔一致性
- **伏笔生成**：
  - 基于情节自动生成伏笔建议
  - 提供伏笔植入位置建议
  - 生成符合情节的伏笔内容

#### 7.11.6 技术实现与集成

**1. 数据库集成**
- **上下文表设计**：
```sql
CREATE TABLE contexts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    context_type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    priority INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

CREATE INDEX idx_contexts_project_type ON contexts(project_id, context_type);
CREATE INDEX idx_contexts_priority ON contexts(priority);
```

**2. AI模型集成**
- **上下文格式化**：将上下文转换为AI模型可理解的格式
- **提示词构建**：基于上下文构建有效的提示词
- **上下文窗口管理**：管理AI模型的上下文窗口大小

**3. 用户界面集成**
- **上下文管理界面**：提供直观的上下文管理界面
- **可视化展示**：以可视化方式展示上下文关系
- **交互式编辑**：支持交互式上下文编辑和调整

### 7.12 向量库检索功能

#### 7.12.1 功能概述
向量库检索功能是AI小说助手的高级智能检索系统，基于先进的语义向量技术，实现对创作内容的深度语义理解和精准检索。通过将文本内容转换为高维向量表示，系统能够理解内容的语义含义，而不仅仅是关键词匹配，从而为创作者提供更加智能和精准的内容检索服务。

#### 7.12.2 检索范围与数据源

**1. 核心内容检索**
- **章节内容**：已完成的章节文本内容
  - 完整章节文本
  - 章节摘要和关键情节
  - 重要对话和描述片段
  - 情节转折点和高潮部分
- **人物设定**：角色信息和背景资料
  - 角色基本信息和外貌描述
  - 性格特征和行为模式
  - 背景故事和成长经历
  - 角色关系和互动记录
- **大纲信息**：故事大纲和章节安排
  - 整体故事梗概
  - 章节结构和情节安排
  - 主要冲突和转折点
  - 故事主题和核心思想

**2. 扩展内容检索**
- **世界观设定**：世界观和背景设定
  - 世界背景和历史设定
  - 规则体系和法则设定
  - 地理环境和社会结构
  - 文化背景和价值观念
- **对话记录**：AI对话历史记录
  - 创作咨询对话
  - 问题解答记录
  - 灵感讨论内容
  - 技巧交流记录
- **提示词库**：提示词模板和示例
  - 内置提示词模板
  - 用户自定义模板
  - 成功案例和示例
  - 优化建议和技巧

**3. 外部资源检索**
- **参考素材**：导入的参考资料
  - 文学作品片段
  - 历史资料和背景信息
  - 专业知识和技术资料
  - 创作灵感和素材收集
- **网络资源**：在线内容检索（可选）
  - 公开文学作品
  - 创作技巧文章
  - 行业资讯和趋势
  - 读者反馈和评论

#### 7.12.3 技术架构与实现

**1. 向量化引擎**
```python
class VectorEmbeddingEngine:
    def __init__(self, model_name="text-embedding-ada-002"):
        self.model_name = model_name
        self.embedding_cache = {}

    async def embed_text(self, text: str, chunk_size: int = 8000) -> List[float]:
        """将文本转换为向量表示"""
        # 检查缓存
        text_hash = hashlib.md5(text.encode()).hexdigest()
        if text_hash in self.embedding_cache:
            return self.embedding_cache[text_hash]

        # 文本预处理
        processed_text = self.preprocess_text(text)

        # 分块处理长文本
        if len(processed_text) > chunk_size:
            chunks = self.split_text(processed_text, chunk_size)
            embeddings = []
            for chunk in chunks:
                chunk_embedding = await self.get_embedding(chunk)
                embeddings.append(chunk_embedding)
            # 合并向量（平均或加权平均）
            final_embedding = self.merge_embeddings(embeddings)
        else:
            final_embedding = await self.get_embedding(processed_text)

        # 缓存结果
        self.embedding_cache[text_hash] = final_embedding
        return final_embedding

    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 清理HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 标准化空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        return text.strip()
```

**2. 向量数据库管理**
```python
class VectorDatabase:
    def __init__(self, db_path: str):
        self.client = chromadb.PersistentClient(path=db_path)
        self.collections = {}

    def create_collection(self, name: str, embedding_function=None):
        """创建向量集合"""
        if embedding_function is None:
            embedding_function = embedding_functions.DefaultEmbeddingFunction()

        collection = self.client.get_or_create_collection(
            name=name,
            embedding_function=embedding_function,
            metadata={"hnsw:space": "cosine"}
        )
        self.collections[name] = collection
        return collection

    def add_documents(self, collection_name: str, documents: List[str],
                     metadatas: List[dict], ids: List[str]):
        """添加文档到向量库"""
        collection = self.collections.get(collection_name)
        if not collection:
            collection = self.create_collection(collection_name)

        # 批量添加文档
        batch_size = 100
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]

            collection.add(
                documents=batch_docs,
                metadatas=batch_metas,
                ids=batch_ids
            )

    def search(self, collection_name: str, query: str,
              n_results: int = 10, where: dict = None) -> dict:
        """语义检索"""
        collection = self.collections.get(collection_name)
        if not collection:
            return {"documents": [], "metadatas": [], "distances": []}

        results = collection.query(
            query_texts=[query],
            n_results=n_results,
            where=where,
            include=["documents", "metadatas", "distances"]
        )

        return {
            "documents": results["documents"][0],
            "metadatas": results["metadatas"][0],
            "distances": results["distances"][0]
        }
```

**3. 智能检索引擎**
```python
class IntelligentSearchEngine:
    def __init__(self, vector_db: VectorDatabase, embedding_engine: VectorEmbeddingEngine):
        self.vector_db = vector_db
        self.embedding_engine = embedding_engine

    async def semantic_search(self, query: str, search_config: dict) -> dict:
        """智能语义检索"""
        # 解析检索配置
        collections = search_config.get("collections", ["chapters", "characters", "outlines"])
        max_results = search_config.get("max_results", 10)
        similarity_threshold = search_config.get("similarity_threshold", 0.7)
        boost_recent = search_config.get("boost_recent", True)

        # 查询向量化
        query_embedding = await self.embedding_engine.embed_text(query)

        # 多集合检索
        all_results = []
        for collection_name in collections:
            results = self.vector_db.search(
                collection_name=collection_name,
                query=query,
                n_results=max_results * 2  # 获取更多结果用于后处理
            )

            # 添加集合标识
            for i, doc in enumerate(results["documents"]):
                all_results.append({
                    "document": doc,
                    "metadata": results["metadatas"][i],
                    "distance": results["distances"][i],
                    "similarity": 1 - results["distances"][i],
                    "collection": collection_name
                })

        # 结果过滤和排序
        filtered_results = [
            r for r in all_results
            if r["similarity"] >= similarity_threshold
        ]

        # 智能排序
        sorted_results = self.intelligent_ranking(
            filtered_results, query, boost_recent
        )

        return {
            "query": query,
            "total_results": len(sorted_results),
            "results": sorted_results[:max_results],
            "search_config": search_config
        }

    def intelligent_ranking(self, results: List[dict], query: str,
                          boost_recent: bool = True) -> List[dict]:
        """智能结果排序"""
        for result in results:
            score = result["similarity"]

            # 时间衰减因子
            if boost_recent and "created_at" in result["metadata"]:
                created_at = datetime.fromisoformat(result["metadata"]["created_at"])
                days_ago = (datetime.now() - created_at).days
                time_factor = max(0.5, 1 - days_ago * 0.01)  # 每天衰减1%
                score *= time_factor

            # 内容类型权重
            collection = result["collection"]
            type_weights = {
                "chapters": 1.0,
                "characters": 0.9,
                "outlines": 0.8,
                "conversations": 0.7,
                "prompts": 0.6
            }
            score *= type_weights.get(collection, 0.5)

            # 内容长度因子
            doc_length = len(result["document"])
            if doc_length < 100:
                score *= 0.8  # 短内容降权
            elif doc_length > 2000:
                score *= 1.1  # 长内容加权

            result["final_score"] = score

        return sorted(results, key=lambda x: x["final_score"], reverse=True)
```

#### 7.12.4 嵌入模型管理

**1. 多模型支持**
- **OpenAI Embeddings**：text-embedding-ada-002, text-embedding-3-small, text-embedding-3-large
- **本地模型**：sentence-transformers系列模型
- **多语言模型**：支持中英文混合内容的嵌入
- **专业模型**：针对文学创作优化的专用嵌入模型

**2. 模型配置管理**
```python
class EmbeddingModelManager:
    def __init__(self):
        self.models = {}
        self.default_model = None

    def register_model(self, name: str, model_config: dict):
        """注册嵌入模型"""
        self.models[name] = {
            "config": model_config,
            "instance": None,
            "performance": {
                "avg_latency": 0,
                "success_rate": 1.0,
                "last_used": None
            }
        }

    def get_model(self, name: str = None):
        """获取嵌入模型实例"""
        model_name = name or self.default_model
        if model_name not in self.models:
            raise ValueError(f"未找到嵌入模型: {model_name}")

        model_info = self.models[model_name]
        if model_info["instance"] is None:
            model_info["instance"] = self.create_model_instance(
                model_info["config"]
            )

        return model_info["instance"]

    def benchmark_models(self, test_texts: List[str]) -> dict:
        """模型性能基准测试"""
        results = {}
        for model_name in self.models:
            model = self.get_model(model_name)
            start_time = time.time()

            try:
                embeddings = [model.embed(text) for text in test_texts]
                latency = (time.time() - start_time) / len(test_texts)
                success_rate = 1.0
            except Exception as e:
                latency = float('inf')
                success_rate = 0.0

            results[model_name] = {
                "avg_latency": latency,
                "success_rate": success_rate,
                "embedding_dim": len(embeddings[0]) if success_rate > 0 else 0
            }

        return results
```

#### 7.12.5 高级检索功能

**1. 混合检索**
- **语义+关键词**：结合语义相似度和关键词匹配
- **多模态检索**：支持文本、图片、音频等多模态内容
- **时间序列检索**：基于时间维度的内容检索
- **关联检索**：基于内容关联性的扩展检索

**2. 智能过滤**
- **相似度阈值**：可调节的相似度阈值设置
- **内容类型过滤**：按内容类型筛选结果
- **时间范围过滤**：按创建时间筛选结果
- **作者过滤**：按创作者筛选结果

**3. 结果优化**
- **去重处理**：智能识别和去除重复内容
- **相关性排序**：基于多维度相关性排序
- **上下文扩展**：提供检索结果的上下文信息
- **摘要生成**：为长文本结果生成摘要

#### 7.12.6 应用场景与使用方法

**1. 创作参考检索**
- **情节参考**：查找相似情节的处理方式
- **角色参考**：查找相似角色的塑造方法
- **场景参考**：查找相似场景的描写技巧
- **对话参考**：查找相似对话的表达方式

**2. 一致性检查**
- **设定一致性**：检查角色设定的前后一致性
- **情节一致性**：检查情节发展的逻辑一致性
- **世界观一致性**：检查世界观设定的一致性
- **风格一致性**：检查写作风格的一致性

**3. 灵感发现**
- **相关内容发现**：发现与当前创作相关的内容
- **潜在关联发现**：发现潜在的内容关联
- **创作灵感触发**：通过相似内容触发创作灵感
- **素材整合**：整合相关素材形成新的创作思路

**4. 质量提升**
- **内容补充**：基于检索结果补充内容
- **表达优化**：参考优质表达方式优化内容
- **结构改进**：参考优秀结构改进内容组织
- **细节丰富**：通过检索丰富内容细节

### 7.13 降AI味功能

#### 7.13.1 功能概述
降AI味功能是AI小说助手的核心特色功能，专门解决AI生成内容机械化、模板化的问题。通过多层次的算法处理和优化策略，将AI生成的内容转化为更接近人类写作风格的自然文本，提升小说的可读性和真实感。

#### 7.13.2 AI味识别机制
**常见AI味特征检测：**
- **重复性表达**：检测高频重复的词汇和句式
- **机械化转折**：识别生硬的逻辑转折和连接词
- **模板化描述**：检测套路化的场景和人物描述
- **情感单调**：识别情感表达的单一性和缺乏层次
- **逻辑过于完美**：检测过于理想化的情节发展

**AI味程度评估：**
- **词汇重复度分析**：统计高频词汇使用频率
- **句式结构分析**：检测句式的多样性程度
- **情感丰富度评估**：分析情感表达的层次性
- **逻辑自然度检测**：评估情节发展的自然程度
- **综合AI味评分**：0-100分，分数越高AI味越重

#### 7.13.3 降AI味处理策略

**1. 语言多样化处理**
- **同义词替换**：将重复使用的词汇替换为同义词
- **句式变换**：改变单调的句式结构，增加表达多样性
- **表达方式优化**：将直白的表达转化为更有文学性的描述
- **语气调节**：根据场景调整语气的正式程度和情感色彩

**2. 情感层次增强**
- **情感细节添加**：在关键情节中增加细腻的情感描述
- **心理活动丰富**：扩展角色的内心独白和心理变化
- **情感冲突强化**：增强角色间的情感张力和冲突
- **情感渐进处理**：让情感变化更加自然和渐进

**3. 细节生动化**
- **感官描述增强**：添加视觉、听觉、触觉等感官细节
- **环境氛围营造**：丰富场景的氛围描述和环境细节
- **动作具体化**：将抽象的动作描述具体化和生动化
- **对话自然化**：让对话更符合角色性格和说话习惯

**4. 逻辑自然化**
- **因果关系优化**：让情节发展的因果关系更加自然
- **冲突合理化**：确保冲突的产生和解决符合逻辑
- **转折自然化**：让剧情转折更加自然，避免突兀感
- **伏笔布局**：合理安排伏笔和呼应，增强故事连贯性

#### 7.13.4 技术实现原理

**1. 文本分析引擎**
```python
class AIFlavorDetector:
    def __init__(self):
        self.repetition_analyzer = RepetitionAnalyzer()
        self.emotion_analyzer = EmotionAnalyzer()
        self.structure_analyzer = StructureAnalyzer()

    def analyze_ai_flavor(self, text):
        """分析文本的AI味程度"""
        repetition_score = self.repetition_analyzer.analyze(text)
        emotion_score = self.emotion_analyzer.analyze(text)
        structure_score = self.structure_analyzer.analyze(text)

        ai_flavor_score = (repetition_score + emotion_score + structure_score) / 3
        return {
            'overall_score': ai_flavor_score,
            'repetition': repetition_score,
            'emotion': emotion_score,
            'structure': structure_score,
            'suggestions': self.generate_suggestions(text)
        }
```

**2. 内容优化引擎**
```python
class ContentOptimizer:
    def __init__(self):
        self.synonym_replacer = SynonymReplacer()
        self.emotion_enhancer = EmotionEnhancer()
        self.detail_enricher = DetailEnricher()

    def optimize_content(self, text, optimization_level='medium'):
        """优化内容，降低AI味"""
        # 多层次优化处理
        optimized_text = text

        # 第一层：词汇多样化
        optimized_text = self.synonym_replacer.replace(optimized_text)

        # 第二层：情感增强
        optimized_text = self.emotion_enhancer.enhance(optimized_text)

        # 第三层：细节丰富化
        optimized_text = self.detail_enricher.enrich(optimized_text)

        return optimized_text
```

#### 7.13.5 应用场景和使用方式

**1. 自动后台处理**
- **章节生成时自动处理**：AI生成章节内容后自动进行降AI味处理
- **实时编辑优化**：用户编辑过程中实时检测和提示AI味问题
- **保存时自动优化**：保存章节时自动进行轻度降AI味处理

**2. 手动批量处理**
- **单章节处理**：选择特定章节进行深度降AI味处理
- **批量章节处理**：对多个章节同时进行降AI味处理
- **全书优化**：对整部小说进行全面的降AI味处理

**3. 质量检测模式**
- **AI味检测报告**：生成详细的AI味分析报告
- **问题定位**：精确定位AI味严重的段落和句子
- **改进建议**：提供具体的修改建议和优化方案

#### 7.13.6 用户控制选项

**处理强度设置：**
- **轻度处理**：保持原文结构，仅进行词汇优化
- **中度处理**：适度调整句式和增加细节描述
- **深度处理**：大幅优化表达方式和情感层次

**保护设置：**
- **专有名词保护**：保护小说中的专有名词不被修改
- **对话风格保护**：保持角色对话的个性化特征
- **关键情节保护**：保护重要情节不被过度修改

**自定义规则：**
- **用户词典**：用户可添加自定义的替换词典
- **风格偏好**：设置偏好的写作风格和表达方式
- **禁用词汇**：设置不希望出现的词汇和表达

### 7.14 设置功能

#### 7.14.1 功能概述
设置功能是AI小说助手的系统配置中心，提供全面的应用配置管理，包括AI模型配置、界面设置、编辑器设置、数据管理等。系统采用分类管理的方式，便于用户快速找到和配置相关选项。

#### 7.14.2 AI模型配置详细示例

**1. OpenAI (GPT) 配置示例**
```ini
[API_KEYS]
gpt_api_key = sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

[MODELS]
# 支持OpenAI全系列模型，用户可自由选择任何可用模型
gpt_model = gpt-4o  # 示例模型，支持所有OpenAI模型：
# GPT-4系列: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-4, gpt-4-32k
# GPT-3.5系列: gpt-3.5-turbo, gpt-3.5-turbo-16k
# 以及OpenAI发布的任何新模型

[OPENAI_SETTINGS]
api_url = https://api.openai.com/v1/chat/completions
max_tokens = 4096
temperature = 0.7
timeout = 30
```

**2. Claude (Anthropic) 配置示例**
```ini
[API_KEYS]
claude_api_key = sk-ant-api03-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

[MODELS]
# 支持Anthropic全系列模型，用户可自由选择任何可用模型
claude_model = claude-3-opus-20240229  # 示例模型，支持所有Claude模型：
# Claude 3系列: claude-3-opus, claude-3-sonnet, claude-3-haiku
# Claude 2系列: claude-2, claude-2.0, claude-2.1
# Claude Instant系列: claude-instant-1.2
# 以及Anthropic发布的任何新模型

[ANTHROPIC_SETTINGS]
api_url = https://api.anthropic.com/v1/messages
max_tokens = 4096
temperature = 0.7
timeout = 30
```

**3. Gemini (Google) 配置示例**
```ini
[API_KEYS]
gemini_api_key = AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

[MODELS]
# 支持Google全系列模型，用户可自由选择任何可用模型
gemini_model = gemini-2.0-flash  # 示例模型，支持所有Gemini模型：
# Gemini 2.0系列: gemini-2.0-flash, gemini-2.0-pro
# Gemini 1.5系列: gemini-1.5-pro, gemini-1.5-flash
# Gemini 1.0系列: gemini-1.0-pro, gemini-1.0-pro-vision
# 以及Google发布的任何新模型

[GOOGLE_SETTINGS]
api_url = https://generativelanguage.googleapis.com/v1beta/models
max_tokens = 4096
temperature = 0.7
timeout = 30
```

**4. ModelScope 配置示例**
```ini
[API_KEYS]
modelscope_api_key = ms-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

[MODELS]
# 支持ModelScope平台所有可用模型，用户可自由选择
modelscope_model = deepseek-ai/DeepSeek-R1  # 示例模型，支持所有ModelScope模型：
# DeepSeek系列: deepseek-ai/DeepSeek-R1, deepseek-ai/deepseek-coder, deepseek-ai/deepseek-chat
# Qwen系列: qwen/Qwen2.5-72B-Instruct, qwen/Qwen2-VL, qwen/Qwen-Audio
# ChatGLM系列: ZhipuAI/chatglm3-6b, ZhipuAI/glm-4-9b-chat
# Baichuan系列: baichuan-inc/Baichuan2-13B-Chat
# 以及ModelScope平台上的任何开源模型

[MODELSCOPE_SETTINGS]
base_url = https://api-inference.modelscope.cn/v1/
api_url = https://api-inference.modelscope.cn/v1/chat/completions
max_tokens = 4096
temperature = 0.7
timeout = 30
```

**5. SiliconFlow 配置示例**
```ini
[API_KEYS]
siliconflow_api_key = sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

[MODELS]
# 支持SiliconFlow平台所有可用模型，用户可自由选择
siliconflow_model = deepseek-ai/DeepSeek-R1  # 示例模型，支持所有SiliconFlow模型：
# DeepSeek系列: deepseek-ai/DeepSeek-R1, deepseek-ai/deepseek-coder, deepseek-ai/deepseek-chat
# Qwen系列: Qwen/Qwen2.5-72B-Instruct, Qwen/Qwen2-VL-72B-Instruct
# Meta系列: meta-llama/Llama-3.1-405B-Instruct, meta-llama/Llama-3.2-90B-Vision-Instruct
# Mistral系列: mistralai/Mistral-7B-Instruct-v0.3, mistralai/Mixtral-8x7B-Instruct-v0.1
# 以及SiliconFlow平台上的任何高性能模型

[SILICONFLOW_SETTINGS]
api_url = https://api.siliconflow.cn/v1/chat/completions
max_tokens = 4096
temperature = 0.7
timeout = 30

# 使用说明：SiliconFlow提供高性能AI模型API服务，需要在官网注册获取API密钥
```

**6. Ollama 本地模型配置示例**
```ini
[MODELS]
# 支持Ollama生态系统中的所有模型，用户可自由选择和下载
ollama_model = llama3.2  # 示例模型，支持所有Ollama模型：
# Llama系列: llama3.2, llama3.1, llama3, llama2, codellama, llama2-uncensored
# Qwen系列: qwen2.5, qwen2, qwen:14b, qwen:32b, qwen:72b
# DeepSeek系列: deepseek-coder, deepseek-chat, deepseek-math
# Mistral系列: mistral, mixtral, mistral-openorca
# Gemma系列: gemma, gemma:2b, gemma:7b
# 以及Ollama模型库中的任何开源模型

[OLLAMA_SETTINGS]
api_url = http://localhost:11434/api/chat
timeout = 60
stream = true

# 使用说明：
# 1. 安装Ollama: https://ollama.ai/
# 2. 浏览模型库: https://ollama.ai/library
# 3. 下载任意模型: ollama pull <model_name>
# 4. 启动服务: ollama serve
# 5. 用户可根据需要下载和使用任何Ollama支持的模型
```

**7. 自定义OpenAI兼容API配置示例**
```ini
[API_KEYS]
custom_openai_api_key = your_custom_api_key_here

[MODELS]
# 支持任何OpenAI兼容接口的模型，用户可自由配置
custom_openai_model = your_custom_model_name_here  # 示例模型，支持所有兼容模型：
# 私有部署模型: 自托管的OpenAI兼容模型
# 第三方API服务: 任何提供OpenAI兼容接口的服务商
# 开源模型API: 如Together.ai, Fireworks.ai, Groq等服务
# 本地API服务: 如LM Studio, FastChat, vLLM等搭建的本地API
# 用户可以配置任何符合OpenAI接口规范的模型服务

[CUSTOM_OPENAI_SETTINGS]
api_url = https://your-custom-openai-compatible-api.com/v1/chat/completions
max_tokens = 4096
temperature = 0.7
timeout = 30

# 使用说明：
# 1. 此配置适用于任何兼容OpenAI API格式的服务
# 2. 用户可以连接自己部署的模型或第三方API服务
# 3. 支持本地部署的开源模型和云端API服务
```

## 8. 辅助功能与基础设施

### 8.1 智能API地址检测功能

#### 8.1.1 功能概述
智能API地址检测功能是AI小说助手的重要辅助功能，专门解决用户在配置AI模型API时经常遇到的地址格式错误、连接失败等问题。通过智能分析和自动纠错，系统能够识别用户输入的API地址中的常见错误，并自动提供正确的地址格式，大幅降低用户配置难度，提升API连接成功率。

#### 8.1.2 检测机制详解

**1. 地址有效性检测**
```python
class APIAddressValidator:
    def __init__(self):
        self.timeout = 10
        self.retry_count = 3

    async def validate_address(self, url: str, api_type: str) -> dict:
        """检测API地址的有效性"""
        validation_result = {
            "is_valid": False,
            "status_code": None,
            "response_time": None,
            "error_message": None,
            "suggestions": []
        }

        try:
            start_time = time.time()

            # 发送测试请求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                test_payload = self.get_test_payload(api_type)

                async with session.post(url, json=test_payload) as response:
                    validation_result["status_code"] = response.status
                    validation_result["response_time"] = time.time() - start_time

                    if response.status == 200:
                        validation_result["is_valid"] = True
                    elif response.status == 401:
                        validation_result["error_message"] = "API密钥无效或缺失"
                    elif response.status == 404:
                        validation_result["error_message"] = "API端点不存在"
                        validation_result["suggestions"] = self.suggest_correct_endpoints(url, api_type)
                    else:
                        validation_result["error_message"] = f"HTTP {response.status}"

        except asyncio.TimeoutError:
            validation_result["error_message"] = "连接超时"
            validation_result["suggestions"] = self.suggest_alternative_addresses(url, api_type)
        except Exception as e:
            validation_result["error_message"] = str(e)
            validation_result["suggestions"] = self.analyze_and_suggest(url, api_type, str(e))

        return validation_result
```

**2. 协议智能检测**
```python
class ProtocolDetector:
    def __init__(self):
        self.secure_providers = ["openai", "anthropic", "google"]

    def detect_and_fix_protocol(self, url: str, provider: str) -> str:
        """智能检测和修复协议"""
        # 移除现有协议
        clean_url = re.sub(r'^https?://', '', url)

        # 根据提供商决定协议
        if provider.lower() in self.secure_providers:
            return f"https://{clean_url}"

        # 检测是否为本地地址
        if self.is_local_address(clean_url):
            return f"http://{clean_url}"

        # 默认使用HTTPS
        return f"https://{clean_url}"

    def is_local_address(self, url: str) -> bool:
        """检测是否为本地地址"""
        local_patterns = [
            r'^localhost',
            r'^127\.0\.0\.1',
            r'^192\.168\.',
            r'^10\.',
            r'^172\.(1[6-9]|2[0-9]|3[0-1])\.'
        ]
        return any(re.match(pattern, url) for pattern in local_patterns)
```

**3. 端口智能检测**
```python
class PortDetector:
    def __init__(self):
        self.default_ports = {
            "openai": 443,
            "anthropic": 443,
            "ollama": 11434,
            "local": 8000
        }

    def detect_and_add_port(self, url: str, provider: str) -> str:
        """智能检测和添加端口"""
        # 检查是否已有端口
        if ':' in url.split('/')[-1] and url.split(':')[-1].isdigit():
            return url

        # 根据提供商添加默认端口
        default_port = self.default_ports.get(provider.lower())
        if default_port and default_port != 443:  # HTTPS默认端口不需要显式指定
            # 在域名后添加端口
            parts = url.split('/')
            if len(parts) >= 3:
                parts[2] += f":{default_port}"
                return '/'.join(parts)

        return url
```

**4. 路径智能纠正**
```python
class PathCorrector:
    def __init__(self):
        self.standard_paths = {
            "openai": {
                "base": "/v1",
                "chat": "/v1/chat/completions",
                "embeddings": "/v1/embeddings"
            },
            "anthropic": {
                "base": "/v1",
                "messages": "/v1/messages"
            },
            "ollama": {
                "base": "/api",
                "generate": "/api/generate",
                "chat": "/api/chat"
            }
        }

    def correct_path(self, url: str, provider: str, endpoint_type: str = "chat") -> str:
        """纠正API路径"""
        provider_paths = self.standard_paths.get(provider.lower(), {})
        if not provider_paths:
            return url

        # 获取标准路径
        standard_path = provider_paths.get(endpoint_type, provider_paths.get("base", ""))
        if not standard_path:
            return url

        # 解析URL
        parsed = urlparse(url)
        base_url = f"{parsed.scheme}://{parsed.netloc}"

        # 检查当前路径是否正确
        if parsed.path == standard_path:
            return url

        # 纠正路径
        corrected_url = base_url + standard_path
        return corrected_url
```

#### 8.1.3 纠正策略与算法

**1. 常见错误模式识别**
```python
class ErrorPatternMatcher:
    def __init__(self):
        self.error_patterns = {
            "missing_protocol": r"^[^:]+\.[^:]+",
            "wrong_protocol": r"^http://api\.(openai|anthropic)\.com",
            "missing_version": r"api\.openai\.com/?$",
            "wrong_endpoint": r"/v1/(completion|generate)s?$",
            "extra_slash": r"//+",
            "missing_slash": r"\.com[^/]",
        }

    def identify_errors(self, url: str) -> List[str]:
        """识别URL中的错误模式"""
        errors = []
        for error_type, pattern in self.error_patterns.items():
            if re.search(pattern, url):
                errors.append(error_type)
        return errors

    def suggest_fixes(self, url: str, errors: List[str], provider: str) -> List[str]:
        """根据错误类型提供修复建议"""
        suggestions = []

        for error in errors:
            if error == "missing_protocol":
                suggestions.append(f"https://{url}")
            elif error == "wrong_protocol":
                suggestions.append(url.replace("http://", "https://"))
            elif error == "missing_version" and provider == "openai":
                suggestions.append(url.rstrip('/') + "/v1/chat/completions")
            # ... 更多修复规则

        return suggestions
```

**2. 智能地址补全**
```python
class AddressCompletion:
    def __init__(self):
        self.completion_rules = {
            "openai": {
                "domain_patterns": ["api.openai.com", "openai.com"],
                "complete_url": "https://api.openai.com/v1/chat/completions"
            },
            "anthropic": {
                "domain_patterns": ["api.anthropic.com", "anthropic.com"],
                "complete_url": "https://api.anthropic.com/v1/messages"
            }
        }

    def complete_address(self, partial_url: str, provider: str) -> str:
        """智能补全API地址"""
        provider_rules = self.completion_rules.get(provider.lower())
        if not provider_rules:
            return partial_url

        # 检查是否匹配域名模式
        for pattern in provider_rules["domain_patterns"]:
            if pattern in partial_url.lower():
                return provider_rules["complete_url"]

        return partial_url
```

#### 8.1.4 统一API管理功能

**1. API配置统一管理**
```python
class UnifiedAPIManager:
    def __init__(self, config_storage):
        self.storage = config_storage
        self.active_configs = {}

    def save_api_config(self, provider: str, config: dict) -> str:
        """保存API配置"""
        config_id = f"{provider}_{int(time.time())}"

        # 验证配置
        validation_result = self.validate_config(config)
        if not validation_result["is_valid"]:
            raise ValueError(f"配置验证失败: {validation_result['error']}")

        # 加密敏感信息
        encrypted_config = self.encrypt_sensitive_data(config)

        # 保存到存储
        self.storage.save({
            "id": config_id,
            "provider": provider,
            "config": encrypted_config,
            "created_at": datetime.now().isoformat(),
            "is_active": True
        })

        return config_id

    def get_api_configs(self, provider: str = None) -> List[dict]:
        """获取API配置列表"""
        configs = self.storage.get_all()

        if provider:
            configs = [c for c in configs if c["provider"] == provider]

        # 解密敏感信息
        for config in configs:
            config["config"] = self.decrypt_sensitive_data(config["config"])

        return configs

    def switch_active_config(self, config_id: str):
        """切换活跃配置"""
        config = self.storage.get(config_id)
        if not config:
            raise ValueError(f"配置不存在: {config_id}")

        # 设置为活跃配置
        self.active_configs[config["provider"]] = config

        # 更新存储状态
        self.storage.update_active_status(config_id, True)
```

**2. 快速切换功能**
```python
class QuickSwitchManager:
    def __init__(self, api_manager: UnifiedAPIManager):
        self.api_manager = api_manager
        self.switch_history = []

    def create_quick_switch_profile(self, name: str, configs: dict) -> str:
        """创建快速切换配置文件"""
        profile = {
            "id": f"profile_{int(time.time())}",
            "name": name,
            "configs": configs,  # {provider: config_id}
            "created_at": datetime.now().isoformat()
        }

        self.api_manager.storage.save_profile(profile)
        return profile["id"]

    def switch_to_profile(self, profile_id: str):
        """切换到指定配置文件"""
        profile = self.api_manager.storage.get_profile(profile_id)
        if not profile:
            raise ValueError(f"配置文件不存在: {profile_id}")

        # 切换所有相关配置
        for provider, config_id in profile["configs"].items():
            self.api_manager.switch_active_config(config_id)

        # 记录切换历史
        self.switch_history.append({
            "profile_id": profile_id,
            "switched_at": datetime.now().isoformat()
        })
```

### 8.2 记忆窗口功能

#### 8.2.1 功能概述
记忆窗口功能是AI小说助手的用户体验优化功能，通过智能记忆用户的窗口使用习惯和偏好设置，实现个性化的界面布局。系统能够自动保存和恢复窗口大小、位置、面板状态等信息，确保用户每次打开应用时都能获得熟悉和舒适的工作环境。

#### 8.2.2 记忆内容详解

**1. 窗口几何信息**
```python
class WindowGeometry:
    def __init__(self):
        self.width = 1200
        self.height = 800
        self.x = 100
        self.y = 100
        self.is_maximized = False
        self.is_minimized = False
        self.is_fullscreen = False

    def to_dict(self) -> dict:
        return {
            "width": self.width,
            "height": self.height,
            "x": self.x,
            "y": self.y,
            "is_maximized": self.is_maximized,
            "is_minimized": self.is_minimized,
            "is_fullscreen": self.is_fullscreen
        }

    @classmethod
    def from_dict(cls, data: dict):
        geometry = cls()
        geometry.width = data.get("width", 1200)
        geometry.height = data.get("height", 800)
        geometry.x = data.get("x", 100)
        geometry.y = data.get("y", 100)
        geometry.is_maximized = data.get("is_maximized", False)
        geometry.is_minimized = data.get("is_minimized", False)
        geometry.is_fullscreen = data.get("is_fullscreen", False)
        return geometry
```

**2. 面板布局状态**
```python
class PanelLayoutState:
    def __init__(self):
        self.left_panel_width = 400  # 左侧面板宽度
        self.right_panel_width = 720  # 右侧面板宽度
        self.panel_states = {
            "navigation": {"visible": True, "collapsed": False},
            "properties": {"visible": True, "collapsed": False},
            "preview": {"visible": True, "collapsed": False},
            "console": {"visible": False, "collapsed": True}
        }
        self.active_tabs = {
            "left": "outline_edit",
            "right": "chapter_edit"
        }

    def update_panel_state(self, panel_name: str, visible: bool, collapsed: bool = None):
        """更新面板状态"""
        if panel_name in self.panel_states:
            self.panel_states[panel_name]["visible"] = visible
            if collapsed is not None:
                self.panel_states[panel_name]["collapsed"] = collapsed
```

**3. 多显示器支持**
```python
class MultiDisplayManager:
    def __init__(self):
        self.displays = []
        self.primary_display = None
        self.current_display = None

    def detect_displays(self) -> List[dict]:
        """检测所有显示器"""
        try:
            # 使用Tauri API获取显示器信息
            displays_info = []
            # 这里会调用Tauri的显示器API
            return displays_info
        except Exception as e:
            logger.error(f"检测显示器失败: {e}")
            return [{"id": 0, "width": 1920, "height": 1080, "x": 0, "y": 0}]

    def validate_window_position(self, geometry: WindowGeometry) -> WindowGeometry:
        """验证窗口位置是否在有效显示器范围内"""
        displays = self.detect_displays()

        # 检查窗口是否在任何显示器范围内
        for display in displays:
            if (display["x"] <= geometry.x < display["x"] + display["width"] and
                display["y"] <= geometry.y < display["y"] + display["height"]):
                return geometry

        # 如果不在任何显示器范围内，移动到主显示器
        primary = displays[0] if displays else {"x": 0, "y": 0}
        geometry.x = primary["x"] + 100
        geometry.y = primary["y"] + 100

        return geometry
```

#### 8.2.3 技术实现架构

**1. 窗口状态监听器**
```python
class WindowStateListener:
    def __init__(self, storage_manager):
        self.storage = storage_manager
        self.last_save_time = 0
        self.save_interval = 1.0  # 1秒保存间隔
        self.pending_changes = {}

    def on_window_resize(self, width: int, height: int):
        """窗口大小改变事件"""
        self.pending_changes.update({
            "width": width,
            "height": height,
            "timestamp": time.time()
        })
        self.schedule_save()

    def on_window_move(self, x: int, y: int):
        """窗口位置改变事件"""
        self.pending_changes.update({
            "x": x,
            "y": y,
            "timestamp": time.time()
        })
        self.schedule_save()

    def on_window_state_change(self, state: str):
        """窗口状态改变事件"""
        state_map = {
            "maximized": {"is_maximized": True, "is_minimized": False},
            "minimized": {"is_minimized": True, "is_maximized": False},
            "normal": {"is_maximized": False, "is_minimized": False},
            "fullscreen": {"is_fullscreen": True}
        }

        if state in state_map:
            self.pending_changes.update(state_map[state])
            self.pending_changes["timestamp"] = time.time()
            self.schedule_save()

    def schedule_save(self):
        """调度保存操作"""
        current_time = time.time()
        if current_time - self.last_save_time >= self.save_interval:
            self.save_changes()

    def save_changes(self):
        """保存变更到存储"""
        if not self.pending_changes:
            return

        try:
            current_config = self.storage.load_window_config()
            current_config.update(self.pending_changes)
            self.storage.save_window_config(current_config)

            self.pending_changes.clear()
            self.last_save_time = time.time()

        except Exception as e:
            logger.error(f"保存窗口配置失败: {e}")
```

**2. 配置存储管理器**
```python
class WindowConfigStorage:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.default_config = {
            "geometry": {
                "width": 1200,
                "height": 800,
                "x": 100,
                "y": 100,
                "is_maximized": False,
                "is_minimized": False,
                "is_fullscreen": False
            },
            "layout": {
                "left_panel_width": 400,
                "right_panel_width": 720,
                "panel_states": {
                    "navigation": {"visible": True, "collapsed": False},
                    "properties": {"visible": True, "collapsed": False}
                }
            },
            "preferences": {
                "remember_tabs": True,
                "restore_session": True,
                "auto_save_interval": 300
            }
        }

    def load_window_config(self) -> dict:
        """加载窗口配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 合并默认配置，确保所有必要字段存在
                return self.merge_with_defaults(config)
            else:
                return self.default_config.copy()

        except Exception as e:
            logger.error(f"加载窗口配置失败: {e}")
            return self.default_config.copy()

    def save_window_config(self, config: dict):
        """保存窗口配置"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)

            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存窗口配置失败: {e}")

    def merge_with_defaults(self, config: dict) -> dict:
        """合并用户配置和默认配置"""
        def deep_merge(default, user):
            result = default.copy()
            for key, value in user.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result

        return deep_merge(self.default_config, config)
```

**3. 会话恢复管理器**
```python
class SessionRestoreManager:
    def __init__(self, storage: WindowConfigStorage):
        self.storage = storage

    def save_session_state(self, session_data: dict):
        """保存会话状态"""
        session_config = {
            "last_project": session_data.get("project_id"),
            "open_tabs": session_data.get("tabs", []),
            "active_tab": session_data.get("active_tab"),
            "scroll_positions": session_data.get("scroll_positions", {}),
            "editor_states": session_data.get("editor_states", {}),
            "timestamp": datetime.now().isoformat()
        }

        config = self.storage.load_window_config()
        config["session"] = session_config
        self.storage.save_window_config(config)

    def restore_session_state(self) -> dict:
        """恢复会话状态"""
        config = self.storage.load_window_config()
        session_config = config.get("session", {})

        # 检查会话是否过期（超过7天）
        if "timestamp" in session_config:
            session_time = datetime.fromisoformat(session_config["timestamp"])
            if (datetime.now() - session_time).days > 7:
                return {}

        return session_config

    def clear_session_state(self):
        """清除会话状态"""
        config = self.storage.load_window_config()
        if "session" in config:
            del config["session"]
            self.storage.save_window_config(config)
```

#### 8.2.4 高级功能特性

**1. 智能布局建议**
```python
class LayoutSuggestionEngine:
    def __init__(self):
        self.usage_patterns = {}

    def analyze_usage_pattern(self, user_actions: List[dict]) -> dict:
        """分析用户使用模式"""
        patterns = {
            "preferred_panel_width": self.calculate_preferred_width(user_actions),
            "frequently_used_tabs": self.get_frequent_tabs(user_actions),
            "optimal_window_size": self.suggest_window_size(user_actions)
        }
        return patterns

    def suggest_layout_optimization(self, current_layout: dict) -> List[dict]:
        """建议布局优化"""
        suggestions = []

        # 基于使用频率建议面板调整
        if self.is_left_panel_underused(current_layout):
            suggestions.append({
                "type": "panel_resize",
                "description": "左侧面板使用频率较低，建议缩小以获得更多编辑空间",
                "action": {"left_panel_width": 300}
            })

        return suggestions
```

**2. 工作区配置文件**
```python
class WorkspaceProfileManager:
    def __init__(self, storage: WindowConfigStorage):
        self.storage = storage

    def create_profile(self, name: str, description: str = "") -> str:
        """创建工作区配置文件"""
        current_config = self.storage.load_window_config()

        profile = {
            "id": f"profile_{int(time.time())}",
            "name": name,
            "description": description,
            "config": current_config,
            "created_at": datetime.now().isoformat()
        }

        self.save_profile(profile)
        return profile["id"]

    def apply_profile(self, profile_id: str):
        """应用工作区配置文件"""
        profile = self.load_profile(profile_id)
        if profile:
            self.storage.save_window_config(profile["config"])
            return True
        return False

    def get_profiles(self) -> List[dict]:
        """获取所有配置文件"""
        try:
            profiles_path = os.path.join(os.path.dirname(self.storage.config_path), "profiles.json")
            if os.path.exists(profiles_path):
                with open(profiles_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return []
```

#### 8.2.5 异常处理与恢复机制

**1. 显示器配置变化处理**
```python
class DisplayChangeHandler:
    def __init__(self, multi_display_manager: MultiDisplayManager):
        self.display_manager = multi_display_manager

    def handle_display_change(self, old_config: dict, new_displays: List[dict]) -> dict:
        """处理显示器配置变化"""
        geometry = WindowGeometry.from_dict(old_config.get("geometry", {}))

        # 验证窗口位置是否仍然有效
        validated_geometry = self.display_manager.validate_window_position(geometry)

        # 如果位置被调整，记录日志
        if (validated_geometry.x != geometry.x or validated_geometry.y != geometry.y):
            logger.info(f"窗口位置已调整: ({geometry.x}, {geometry.y}) -> ({validated_geometry.x}, {validated_geometry.y})")

        # 更新配置
        new_config = old_config.copy()
        new_config["geometry"] = validated_geometry.to_dict()

        return new_config
```

**2. 配置损坏恢复**
```python
class ConfigRecoveryManager:
    def __init__(self, storage: WindowConfigStorage):
        self.storage = storage

    def create_backup(self, config: dict):
        """创建配置备份"""
        backup_path = self.storage.config_path + ".backup"
        try:
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")

    def recover_from_backup(self) -> dict:
        """从备份恢复配置"""
        backup_path = self.storage.config_path + ".backup"
        try:
            if os.path.exists(backup_path):
                with open(backup_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"从备份恢复配置失败: {e}")

        return self.storage.default_config.copy()
```

### 8.3 应用运行日志系统

#### 8.3.1 功能概述
应用运行日志系统是AI小说助手的重要基础设施，负责记录应用运行过程中的所有重要事件、错误信息、用户操作和系统状态。系统采用纯中文日志显示，便于中文用户理解和排查问题，同时提供多级别日志记录、实时日志查看、日志分析和导出等功能。

#### 8.3.2 日志系统架构

**1. 日志记录器**
```python
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class ChineseLogger:
    def __init__(self, name: str, log_file: str = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # 中文日志级别映射
        self.level_names = {
            logging.DEBUG: "调试",
            logging.INFO: "信息",
            logging.WARNING: "警告",
            logging.ERROR: "错误",
            logging.CRITICAL: "严重错误"
        }

        # 设置日志格式
        self.setup_formatters()

        # 设置处理器
        self.setup_handlers(log_file)

    def setup_formatters(self):
        """设置日志格式器"""
        # 控制台格式器（简洁）
        self.console_formatter = logging.Formatter(
            '%(asctime)s [%(chinese_level)s] %(chinese_module)s: %(chinese_message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 文件格式器（详细）
        self.file_formatter = logging.Formatter(
            '%(asctime)s [%(chinese_level)s] %(chinese_module)s.%(funcName)s:%(lineno)d - %(chinese_message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    def setup_handlers(self, log_file: str = None):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ChineseLogFormatter(self.console_formatter, self.level_names))
        self.logger.addHandler(console_handler)

        # 文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(ChineseLogFormatter(self.file_formatter, self.level_names))
            self.logger.addHandler(file_handler)

    def debug(self, message: str, module: str = "系统", **kwargs):
        """调试日志"""
        self._log(logging.DEBUG, message, module, **kwargs)

    def info(self, message: str, module: str = "系统", **kwargs):
        """信息日志"""
        self._log(logging.INFO, message, module, **kwargs)

    def warning(self, message: str, module: str = "系统", **kwargs):
        """警告日志"""
        self._log(logging.WARNING, message, module, **kwargs)

    def error(self, message: str, module: str = "系统", **kwargs):
        """错误日志"""
        self._log(logging.ERROR, message, module, **kwargs)

    def critical(self, message: str, module: str = "系统", **kwargs):
        """严重错误日志"""
        self._log(logging.CRITICAL, message, module, **kwargs)

    def _log(self, level: int, message: str, module: str, **kwargs):
        """内部日志记录方法"""
        extra = {
            'chinese_level': self.level_names[level],
            'chinese_module': module,
            'chinese_message': message,
            **kwargs
        }
        self.logger.log(level, message, extra=extra)

class ChineseLogFormatter(logging.Formatter):
    def __init__(self, formatter: logging.Formatter, level_names: Dict[int, str]):
        super().__init__()
        self.formatter = formatter
        self.level_names = level_names

    def format(self, record):
        # 添加中文字段
        record.chinese_level = self.level_names.get(record.levelno, "未知")
        record.chinese_module = getattr(record, 'chinese_module', '系统')
        record.chinese_message = getattr(record, 'chinese_message', record.getMessage())

        return self.formatter.format(record)
```

**2. 模块化日志管理**
```python
class ModuleLogManager:
    def __init__(self, base_log_dir: str):
        self.base_log_dir = base_log_dir
        self.loggers = {}
        self.module_names = {
            "ai_service": "AI服务",
            "database": "数据库",
            "ui": "用户界面",
            "file_manager": "文件管理",
            "api_client": "API客户端",
            "vector_db": "向量数据库",
            "context_manager": "上下文管理",
            "prompt_manager": "提示词管理"
        }

    def get_logger(self, module: str) -> ChineseLogger:
        """获取模块日志记录器"""
        if module not in self.loggers:
            log_file = os.path.join(self.base_log_dir, f"{module}.log")
            self.loggers[module] = ChineseLogger(
                name=module,
                log_file=log_file
            )

        return self.loggers[module]

    def log_user_action(self, action: str, details: Dict[str, Any] = None):
        """记录用户操作"""
        ui_logger = self.get_logger("ui")
        details_str = json.dumps(details, ensure_ascii=False) if details else ""
        ui_logger.info(f"用户操作: {action} {details_str}", module="用户界面")

    def log_ai_request(self, model: str, prompt_length: int, response_length: int, duration: float):
        """记录AI请求"""
        ai_logger = self.get_logger("ai_service")
        ai_logger.info(
            f"AI请求完成 - 模型: {model}, 提示词长度: {prompt_length}, 响应长度: {response_length}, 耗时: {duration:.2f}秒",
            module="AI服务"
        )

    def log_database_operation(self, operation: str, table: str, affected_rows: int = 0):
        """记录数据库操作"""
        db_logger = self.get_logger("database")
        db_logger.info(
            f"数据库操作: {operation} - 表: {table}, 影响行数: {affected_rows}",
            module="数据库"
        )
```

**3. 实时日志查看器**
```python
class RealTimeLogViewer:
    def __init__(self, log_file: str):
        self.log_file = log_file
        self.observers = []
        self.is_watching = False

    def add_observer(self, callback):
        """添加日志观察者"""
        self.observers.append(callback)

    def start_watching(self):
        """开始监控日志文件"""
        self.is_watching = True
        threading.Thread(target=self._watch_log_file, daemon=True).start()

    def stop_watching(self):
        """停止监控日志文件"""
        self.is_watching = False

    def _watch_log_file(self):
        """监控日志文件变化"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                # 移动到文件末尾
                f.seek(0, 2)

                while self.is_watching:
                    line = f.readline()
                    if line:
                        # 解析日志行
                        log_entry = self._parse_log_line(line.strip())
                        if log_entry:
                            # 通知所有观察者
                            for callback in self.observers:
                                try:
                                    callback(log_entry)
                                except Exception as e:
                                    print(f"日志观察者回调错误: {e}")
                    else:
                        time.sleep(0.1)

        except Exception as e:
            print(f"日志文件监控错误: {e}")

    def _parse_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析日志行"""
        try:
            # 使用正则表达式解析日志格式
            pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(.+?)\] (.+?): (.+)'
            match = re.match(pattern, line)

            if match:
                return {
                    "timestamp": match.group(1),
                    "level": match.group(2),
                    "module": match.group(3),
                    "message": match.group(4),
                    "raw_line": line
                }
        except Exception:
            pass

        return None
```

#### 8.3.3 日志分类与管理

**1. 日志分类系统**
```python
class LogCategoryManager:
    def __init__(self):
        self.categories = {
            "系统启动": {
                "description": "应用启动和初始化相关日志",
                "keywords": ["启动", "初始化", "加载", "配置"],
                "importance": "high"
            },
            "用户操作": {
                "description": "用户界面操作和交互日志",
                "keywords": ["点击", "输入", "选择", "操作"],
                "importance": "medium"
            },
            "AI服务": {
                "description": "AI模型调用和响应日志",
                "keywords": ["AI", "生成", "模型", "API"],
                "importance": "high"
            },
            "数据库": {
                "description": "数据库操作和查询日志",
                "keywords": ["数据库", "查询", "插入", "更新"],
                "importance": "medium"
            },
            "错误异常": {
                "description": "系统错误和异常日志",
                "keywords": ["错误", "异常", "失败", "超时"],
                "importance": "critical"
            },
            "性能监控": {
                "description": "系统性能和资源使用日志",
                "keywords": ["性能", "内存", "CPU", "耗时"],
                "importance": "low"
            }
        }

    def categorize_log(self, log_entry: Dict[str, Any]) -> str:
        """对日志条目进行分类"""
        message = log_entry.get("message", "").lower()
        level = log_entry.get("level", "")

        # 优先根据日志级别分类
        if level in ["错误", "严重错误"]:
            return "错误异常"

        # 根据关键词分类
        for category, info in self.categories.items():
            for keyword in info["keywords"]:
                if keyword in message:
                    return category

        return "其他"
```

**2. 日志轮转管理**
```python
class LogRotationManager:
    def __init__(self, log_dir: str, max_file_size: int = 10*1024*1024, max_files: int = 10):
        self.log_dir = log_dir
        self.max_file_size = max_file_size  # 10MB
        self.max_files = max_files

    def check_and_rotate_logs(self):
        """检查并轮转日志文件"""
        for log_file in os.listdir(self.log_dir):
            if log_file.endswith('.log'):
                file_path = os.path.join(self.log_dir, log_file)

                if os.path.getsize(file_path) > self.max_file_size:
                    self._rotate_log_file(file_path)

    def _rotate_log_file(self, file_path: str):
        """轮转单个日志文件"""
        base_name = file_path[:-4]  # 移除.log扩展名

        # 移动现有的轮转文件
        for i in range(self.max_files - 1, 0, -1):
            old_file = f"{base_name}.{i}.log"
            new_file = f"{base_name}.{i+1}.log"

            if os.path.exists(old_file):
                if i == self.max_files - 1:
                    os.remove(old_file)  # 删除最老的文件
                else:
                    os.rename(old_file, new_file)

        # 轮转当前文件
        os.rename(file_path, f"{base_name}.1.log")

        # 创建新的日志文件
        open(file_path, 'w', encoding='utf-8').close()
```

#### 8.3.4 日志分析与报告

**1. 日志分析引擎**
```python
class LogAnalysisEngine:
    def __init__(self, log_manager: ModuleLogManager):
        self.log_manager = log_manager

    def generate_daily_report(self, date: str) -> Dict[str, Any]:
        """生成日常报告"""
        logs = self._get_logs_by_date(date)

        report = {
            "日期": date,
            "总日志条数": len(logs),
            "错误统计": self._count_errors(logs),
            "用户活跃度": self._analyze_user_activity(logs),
            "AI服务使用": self._analyze_ai_usage(logs),
            "性能指标": self._analyze_performance(logs),
            "异常事件": self._find_anomalies(logs)
        }

        return report

    def _count_errors(self, logs: List[Dict]) -> Dict[str, int]:
        """统计错误数量"""
        error_counts = {"错误": 0, "警告": 0, "严重错误": 0}

        for log in logs:
            level = log.get("level", "")
            if level in error_counts:
                error_counts[level] += 1

        return error_counts

    def _analyze_user_activity(self, logs: List[Dict]) -> Dict[str, Any]:
        """分析用户活跃度"""
        user_actions = [log for log in logs if "用户操作" in log.get("message", "")]

        return {
            "操作总数": len(user_actions),
            "活跃时段": self._find_active_hours(user_actions),
            "常用功能": self._find_popular_features(user_actions)
        }

    def _analyze_ai_usage(self, logs: List[Dict]) -> Dict[str, Any]:
        """分析AI服务使用情况"""
        ai_logs = [log for log in logs if log.get("module") == "AI服务"]

        total_requests = len(ai_logs)
        if total_requests == 0:
            return {"请求总数": 0}

        # 提取响应时间
        response_times = []
        for log in ai_logs:
            message = log.get("message", "")
            time_match = re.search(r'耗时: ([\d.]+)秒', message)
            if time_match:
                response_times.append(float(time_match.group(1)))

        return {
            "请求总数": total_requests,
            "平均响应时间": sum(response_times) / len(response_times) if response_times else 0,
            "最长响应时间": max(response_times) if response_times else 0,
            "最短响应时间": min(response_times) if response_times else 0
        }
```

**2. 日志导出功能**
```python
class LogExportManager:
    def __init__(self, log_dir: str):
        self.log_dir = log_dir

    def export_logs(self, start_date: str, end_date: str,
                   format: str = "txt", filters: Dict = None) -> str:
        """导出日志"""
        logs = self._collect_logs(start_date, end_date, filters)

        if format == "txt":
            return self._export_as_text(logs)
        elif format == "json":
            return self._export_as_json(logs)
        elif format == "csv":
            return self._export_as_csv(logs)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def _export_as_text(self, logs: List[Dict]) -> str:
        """导出为文本格式"""
        output = []
        output.append("=" * 80)
        output.append(f"AI小说助手日志导出")
        output.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output.append(f"日志条数: {len(logs)}")
        output.append("=" * 80)
        output.append("")

        for log in logs:
            output.append(f"时间: {log.get('timestamp', '')}")
            output.append(f"级别: {log.get('level', '')}")
            output.append(f"模块: {log.get('module', '')}")
            output.append(f"消息: {log.get('message', '')}")
            output.append("-" * 40)

        return "\n".join(output)

    def _export_as_json(self, logs: List[Dict]) -> str:
        """导出为JSON格式"""
        export_data = {
            "export_info": {
                "timestamp": datetime.now().isoformat(),
                "total_logs": len(logs),
                "application": "AI小说助手"
            },
            "logs": logs
        }

        return json.dumps(export_data, ensure_ascii=False, indent=2)
```

## 9. 数据库设计

### 8.1 数据库概述

#### 8.1.1 数据库选择
本项目采用SQLite作为主要数据库，原因如下：
- **轻量级**：无需独立服务器，适合桌面应用
- **零配置**：无需安装和配置，开箱即用
- **跨平台**：支持Windows、macOS、Linux
- **ACID兼容**：支持事务处理，数据安全可靠
- **SQL标准**：支持标准SQL语法，便于开发

#### 8.1.2 数据库架构
```
ai_novel_assistant.db
├── 核心数据表
│   ├── projects (项目表)
│   ├── outlines (大纲表)
│   ├── chapters (章节表)
│   ├── characters (角色表)
│   └── relationships (关系表)
├── AI相关表
│   ├── ai_models (AI模型表)
│   ├── prompts (提示词表)
│   ├── conversations (对话表)
│   └── embeddings (向量表)
└── 系统配置表
    ├── settings (设置表)
    ├── templates (模板表)
    └── logs (日志表)
```

### 8.2 核心数据表设计

#### 8.2.1 项目表 (projects)
```sql
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    genre VARCHAR(100),
    theme VARCHAR(100),
    style VARCHAR(100),
    target_chapters INTEGER DEFAULT 10,
    target_words_per_chapter INTEGER DEFAULT 3500,
    total_words INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    settings JSON,
    metadata JSON
);

-- 索引
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_at ON projects(created_at);
```

#### 8.2.2 大纲表 (outlines)
```sql
CREATE TABLE outlines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    title VARCHAR(255),
    theme TEXT,
    summary TEXT,
    worldview TEXT,
    chapter_structure JSON,
    character_list JSON,
    version INTEGER DEFAULT 1,
    is_current BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_outlines_project_id ON outlines(project_id);
CREATE INDEX idx_outlines_version ON outlines(project_id, version);
```

#### 8.2.3 章节表 (chapters)
```sql
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    chapter_number INTEGER NOT NULL,
    title VARCHAR(255),
    summary TEXT,
    content TEXT,
    word_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    quality_score REAL,
    ai_generated BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 索引
CREATE UNIQUE INDEX idx_chapters_project_chapter ON chapters(project_id, chapter_number);
CREATE INDEX idx_chapters_status ON chapters(status);
```

#### 8.2.4 角色表 (characters)
```sql
CREATE TABLE characters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) DEFAULT 'supporting',
    gender VARCHAR(20),
    age INTEGER,
    occupation VARCHAR(100),
    description TEXT,
    appearance TEXT,
    personality TEXT,
    background TEXT,
    skills TEXT,
    importance_level INTEGER DEFAULT 3,
    appearance_frequency VARCHAR(50) DEFAULT 'occasional',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_characters_type ON characters(type);
CREATE INDEX idx_characters_importance ON characters(importance_level);
```

#### 8.2.5 关系表 (relationships)
```sql
CREATE TABLE relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    character1_id INTEGER NOT NULL,
    character2_id INTEGER NOT NULL,
    relationship_type VARCHAR(50) NOT NULL,
    strength INTEGER DEFAULT 3,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (character1_id) REFERENCES characters(id) ON DELETE CASCADE,
    FOREIGN KEY (character2_id) REFERENCES characters(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_relationships_project_id ON relationships(project_id);
CREATE INDEX idx_relationships_characters ON relationships(character1_id, character2_id);
CREATE INDEX idx_relationships_type ON relationships(relationship_type);
```

### 8.3 AI相关数据表

#### 8.3.1 AI模型表 (ai_models)
```sql
CREATE TABLE ai_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(100) NOT NULL,
    model_id VARCHAR(255) NOT NULL,
    api_key VARCHAR(500),
    api_url VARCHAR(500),
    max_tokens INTEGER DEFAULT 4000,
    temperature REAL DEFAULT 0.7,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    config JSON
);

-- 索引
CREATE INDEX idx_ai_models_provider ON ai_models(provider);
CREATE INDEX idx_ai_models_active ON ai_models(is_active);
```

#### 8.3.2 提示词表 (prompts)
```sql
CREATE TABLE prompts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    content TEXT NOT NULL,
    variables JSON,
    applicable_models JSON,
    quality_rating INTEGER DEFAULT 3,
    usage_count INTEGER DEFAULT 0,
    is_builtin BOOLEAN DEFAULT FALSE,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    tags VARCHAR(500)
);

-- 索引
CREATE INDEX idx_prompts_category ON prompts(category);
CREATE INDEX idx_prompts_builtin ON prompts(is_builtin);
CREATE INDEX idx_prompts_favorite ON prompts(is_favorite);
```

#### 8.3.3 对话表 (conversations)
```sql
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    title VARCHAR(255),
    ai_model_id INTEGER,
    messages JSON NOT NULL,
    context JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
    FOREIGN KEY (ai_model_id) REFERENCES ai_models(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_conversations_project_id ON conversations(project_id);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
```

### 8.4 系统配置表

#### 8.4.1 设置表 (settings)
```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category VARCHAR(100) NOT NULL,
    key VARCHAR(255) NOT NULL,
    value TEXT,
    data_type VARCHAR(50) DEFAULT 'string',
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE UNIQUE INDEX idx_settings_category_key ON settings(category, key);
```

#### 8.4.2 模板表 (templates)
```sql
CREATE TABLE templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    description TEXT,
    variables JSON,
    is_builtin BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_templates_type ON templates(type);
CREATE INDEX idx_templates_builtin ON templates(is_builtin);
```

#### 8.4.3 日志表 (logs)
```sql
CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    module VARCHAR(100),
    function VARCHAR(100),
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata JSON
);

-- 索引
CREATE INDEX idx_logs_level ON logs(level);
CREATE INDEX idx_logs_created_at ON logs(created_at);
CREATE INDEX idx_logs_module ON logs(module);
```

## 10. API集成方案

### 10.1 AI模型API集成

#### 10.1.1 OpenAI API集成
```python
class OpenAIService:
    def __init__(self, api_key: str, model: str = "gpt-4o"):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        self.supported_models = []

    async def get_available_models(self) -> List[str]:
        """获取所有可用的OpenAI模型"""
        try:
            models = await self.client.models.list()
            return [model.id for model in models.data if 'gpt' in model.id.lower()]
        except Exception as e:
            logger.warning(f"无法获取模型列表: {e}")
            # 返回常见模型作为备选
            return [
                "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-4-32k",
                "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
            ]

    def set_model(self, model: str):
        """动态设置模型，支持用户自由选择任何OpenAI模型"""
        self.model = model
        logger.info(f"已切换到模型: {model}")

    async def generate_text(self, prompt: str, max_tokens: int = 2000, **kwargs) -> str:
        """生成文本，支持动态参数和任意模型"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,  # 支持用户指定的任何模型
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=kwargs.get('temperature', 0.7),
                top_p=kwargs.get('top_p', 1.0),
                frequency_penalty=kwargs.get('frequency_penalty', 0),
                presence_penalty=kwargs.get('presence_penalty', 0)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise APIException(f"OpenAI API调用失败: {e}")
```

#### 10.1.2 Claude API集成
```python
class ClaudeService:
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229"):
        self.client = Anthropic(api_key=api_key)
        self.model = model

    def get_available_models(self) -> List[str]:
        """获取所有可用的Claude模型"""
        # Claude模型列表（Anthropic不提供动态获取API，所以维护常见模型列表）
        return [
            # Claude 3系列
            "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307",
            # Claude 2系列
            "claude-2.1", "claude-2.0", "claude-2",
            # Claude Instant系列
            "claude-instant-1.2", "claude-instant-1.1", "claude-instant-1.0"
            # 注意：用户可以使用任何Anthropic发布的新模型，不限于此列表
        ]

    def set_model(self, model: str):
        """动态设置模型，支持用户自由选择任何Claude模型"""
        self.model = model
        logger.info(f"已切换到Claude模型: {model}")

    async def generate_text(self, prompt: str, max_tokens: int = 2000, **kwargs) -> str:
        """生成文本，支持动态参数和任意Claude模型"""
        try:
            response = await self.client.messages.create(
                model=self.model,  # 支持用户指定的任何Claude模型
                max_tokens=max_tokens,
                temperature=kwargs.get('temperature', 0.7),
                top_p=kwargs.get('top_p', 1.0),
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Claude API error: {e}")
            raise APIException(f"Claude API调用失败: {e}")
```

#### 10.1.3 Gemini API集成
```python
class GeminiService:
    def __init__(self, api_key: str, model: str = "gemini-2.0-flash"):
        genai.configure(api_key=api_key)
        self.model_name = model
        self.model = genai.GenerativeModel(model)

    def get_available_models(self) -> List[str]:
        """获取所有可用的Gemini模型"""
        try:
            models = genai.list_models()
            return [model.name.replace('models/', '') for model in models
                   if 'gemini' in model.name.lower()]
        except Exception as e:
            logger.warning(f"无法获取Gemini模型列表: {e}")
            # 返回常见模型作为备选
            return [
                "gemini-2.0-flash", "gemini-2.0-pro",
                "gemini-1.5-pro", "gemini-1.5-flash",
                "gemini-1.0-pro", "gemini-1.0-pro-vision"
            ]

    def set_model(self, model: str):
        """动态设置模型，支持用户自由选择任何Gemini模型"""
        self.model_name = model
        self.model = genai.GenerativeModel(model)
        logger.info(f"已切换到Gemini模型: {model}")

    async def generate_text(self, prompt: str, max_tokens: int = 2000, **kwargs) -> str:
        """生成文本，支持动态参数和任意Gemini模型"""
        try:
            response = await self.model.generate_content_async(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=max_tokens,
                    temperature=kwargs.get('temperature', 0.7),
                    top_p=kwargs.get('top_p', 1.0),
                    top_k=kwargs.get('top_k', 40)
                )
            )
            return response.text
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            raise APIException(f"Gemini API调用失败: {e}")
```

### 9.2 项目文件管理系统

#### 9.2.1 .ainovel文件格式

AI小说助手使用自定义的.ainovel文件格式来保存项目数据，该格式基于JSON结构，包含项目的完整信息：

```json
{
  "project_info": {
    "name": "项目名称",
    "version": "1.0.0",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "author": "作者名称",
    "description": "项目描述"
  },
  "novel_settings": {
    "title": "小说标题",
    "type": "玄幻奇幻",
    "theme": "成长励志",
    "style": "热血激昂",
    "target_chapters": 100,
    "target_words_per_chapter": 3500,
    "total_words": 350000
  },
  "outline": {
    "summary": "故事梗概",
    "world_setting": "世界观设定",
    "main_theme": "核心主题",
    "plot_structure": {
      "beginning": "开端设定",
      "development": "发展过程",
      "climax": "高潮部分",
      "ending": "结局安排"
    }
  },
  "characters": [
    {
      "id": "char_001",
      "name": "角色姓名",
      "type": "主角",
      "description": "角色描述",
      "background": "背景故事",
      "personality": "性格特点",
      "appearance": "外貌描述",
      "relationships": [
        {
          "target_id": "char_002",
          "relationship": "朋友",
          "description": "关系描述"
        }
      ]
    }
  ],
  "chapters": [
    {
      "id": "chapter_001",
      "number": 1,
      "title": "第一章：开端",
      "summary": "章节摘要",
      "content": "章节内容",
      "word_count": 3500,
      "status": "completed",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T11:00:00Z"
    }
  ],
  "ai_settings": {
    "default_model": "gpt-4-turbo",
    "generation_settings": {
      "temperature": 0.7,
      "max_tokens": 4000,
      "top_p": 0.9
    }
  },
  "metadata": {
    "file_version": "2.0",
    "app_version": "2.0.0",
    "checksum": "md5_hash_value"
  }
}
```

#### 9.2.2 文件操作管理器

```python
class ProjectFileManager:
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.current_project = None

    def save_project(self, project_data: dict, file_path: str) -> bool:
        """保存项目为.ainovel文件"""
        try:
            # 添加元数据
            project_data["metadata"] = {
                "file_version": "2.0",
                "app_version": "2.0.0",
                "saved_at": datetime.now().isoformat(),
                "checksum": self.calculate_checksum(project_data)
            }

            # 确保文件扩展名正确
            if not file_path.endswith('.ainovel'):
                file_path += '.ainovel'

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, indent=2, ensure_ascii=False)

            logger.info(f"项目已保存: {file_path}")
            return True

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            return False

    def load_project(self, file_path: str) -> dict:
        """加载.ainovel项目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # 验证文件格式
            if not self.validate_project_file(project_data):
                raise ValueError("项目文件格式不正确")

            # 检查版本兼容性
            self.check_version_compatibility(project_data)

            self.current_project = project_data
            logger.info(f"项目已加载: {file_path}")
            return project_data

        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            raise

    def export_project(self, project_data: dict, format: str, output_path: str):
        """导出项目为其他格式"""
        if format == "txt":
            self.export_as_text(project_data, output_path)
        elif format == "docx":
            self.export_as_word(project_data, output_path)
        elif format == "pdf":
            self.export_as_pdf(project_data, output_path)
        elif format == "epub":
            self.export_as_epub(project_data, output_path)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def export_as_text(self, project_data: dict, output_path: str):
        """导出为纯文本格式"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入标题
                f.write(f"{project_data['novel_settings']['title']}\n")
                f.write("=" * 50 + "\n\n")

                # 写入基本信息
                f.write(f"作者: {project_data['project_info'].get('author', '未知')}\n")
                f.write(f"类型: {project_data['novel_settings']['type']}\n")
                f.write(f"主题: {project_data['novel_settings']['theme']}\n\n")

                # 写入故事梗概
                if 'outline' in project_data:
                    f.write("故事梗概:\n")
                    f.write(project_data['outline'].get('summary', '') + "\n\n")

                # 写入章节内容
                chapters = sorted(project_data.get('chapters', []),
                                key=lambda x: x['number'])
                for chapter in chapters:
                    f.write(f"{chapter['title']}\n")
                    f.write("-" * 30 + "\n")
                    f.write(chapter.get('content', '') + "\n\n")

            logger.info(f"项目已导出为文本: {output_path}")

        except Exception as e:
            logger.error(f"导出文本失败: {e}")
            raise

    def validate_project_file(self, project_data: dict) -> bool:
        """验证项目文件格式"""
        required_fields = ['project_info', 'novel_settings', 'metadata']

        for field in required_fields:
            if field not in project_data:
                return False

        return True

    def calculate_checksum(self, data: dict) -> str:
        """计算数据校验和"""
        import hashlib
        data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(data_str.encode()).hexdigest()
```

#### 9.2.3 自动备份系统

```python
class AutoBackupManager:
    def __init__(self, backup_dir: str, max_backups: int = 10):
        self.backup_dir = backup_dir
        self.max_backups = max_backups
        os.makedirs(backup_dir, exist_ok=True)

    def create_backup(self, project_data: dict, project_name: str):
        """创建项目备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{project_name}_backup_{timestamp}.ainovel"
        backup_path = os.path.join(self.backup_dir, backup_filename)

        # 保存备份
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, indent=2, ensure_ascii=False)

        # 清理旧备份
        self.cleanup_old_backups(project_name)

        logger.info(f"已创建备份: {backup_path}")
        return backup_path

    def cleanup_old_backups(self, project_name: str):
        """清理旧备份文件"""
        pattern = f"{project_name}_backup_*.ainovel"
        backup_files = glob.glob(os.path.join(self.backup_dir, pattern))

        # 按修改时间排序
        backup_files.sort(key=os.path.getmtime, reverse=True)

        # 删除超出限制的备份
        for backup_file in backup_files[self.max_backups:]:
            os.remove(backup_file)
            logger.info(f"已删除旧备份: {backup_file}")
```

### 9.3 统一AI服务接口

#### 9.2.1 AI服务管理器
```python
class AIServiceManager:
    def __init__(self):
        self.services = {}
        self.default_service = None

    def register_service(self, name: str, service: BaseAIService):
        self.services[name] = service
        if self.default_service is None:
            self.default_service = name

    async def generate(self, prompt: str, model: str = None, **kwargs) -> str:
        service_name = model or self.default_service
        if service_name not in self.services:
            raise ValueError(f"未找到AI服务: {service_name}")

        service = self.services[service_name]
        return await service.generate_text(prompt, **kwargs)
```

#### 9.2.2 API配置管理
```python
class APIConfigManager:
    def __init__(self, db_path: str):
        self.db = Database(db_path)

    def save_api_config(self, provider: str, config: dict):
        encrypted_config = self.encrypt_sensitive_data(config)
        self.db.execute(
            "INSERT OR REPLACE INTO ai_models (provider, config) VALUES (?, ?)",
            (provider, json.dumps(encrypted_config))
        )

    def get_api_config(self, provider: str) -> dict:
        result = self.db.fetch_one(
            "SELECT config FROM ai_models WHERE provider = ?",
            (provider,)
        )
        if result:
            return self.decrypt_sensitive_data(json.loads(result['config']))
        return {}
```

### 9.3 智能API地址检测

#### 9.3.1 地址检测算法
```python
class APIAddressDetector:
    def __init__(self):
        self.common_patterns = {
            'openai': [
                'https://api.openai.com/v1',
                'https://api.openai.com/v1/chat/completions'
            ],
            'anthropic': [
                'https://api.anthropic.com/v1',
                'https://api.anthropic.com/v1/messages'
            ]
        }

    def detect_and_correct(self, url: str, provider: str) -> str:
        # 检测协议
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # 检测和纠正路径
        corrected_url = self.correct_path(url, provider)

        # 验证连接
        if self.test_connection(corrected_url):
            return corrected_url

        # 尝试备用地址
        for pattern in self.common_patterns.get(provider, []):
            if self.test_connection(pattern):
                return pattern

        raise APIException(f"无法连接到{provider} API")
```

### 9.4 向量数据库集成

#### 9.4.1 ChromaDB集成
```python
class VectorDatabase:
    def __init__(self, db_path: str):
        self.client = chromadb.PersistentClient(path=db_path)
        self.collection = self.client.get_or_create_collection(
            name="novel_content",
            embedding_function=embedding_functions.DefaultEmbeddingFunction()
        )

    def add_document(self, doc_id: str, content: str, metadata: dict):
        self.collection.add(
            documents=[content],
            metadatas=[metadata],
            ids=[doc_id]
        )

    def search(self, query: str, n_results: int = 5) -> list:
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results
        )
        return results
```

---

## 10. 开发路线规划

### 10.1 开发阶段概述

本项目采用敏捷开发模式，分为5个主要阶段，每个阶段都有明确的目标和交付物。确保所有14个核心功能模块都在开发路线中得到完整实现。

### 10.2 第一阶段：基础框架搭建

#### 10.2.1 项目初始化
- [ ] 创建Tauri项目结构
- [ ] 配置Vue 3前端环境
- [ ] 设置Python后端环境
- [ ] 配置开发工具和代码规范
- [ ] 建立版本控制系统

#### 10.2.2 核心架构设计
- [ ] 设计应用架构和模块划分
- [ ] 实现Tauri与Python的通信机制
- [ ] 建立前后端API接口规范
- [ ] 实现基础的错误处理机制
- [ ] 实现应用运行日志系统（纯中文日志）

#### 10.2.3 数据存储基础
- [ ] 设计SQLite数据库结构
- [ ] 实现数据库连接和ORM
- [ ] 创建数据迁移脚本
- [ ] 实现基础的CRUD操作
- [ ] 设计数据备份机制
- [ ] 实现.ainovel文件格式支持

#### 10.2.4 系统配置管理
- [ ] 实现配置文件管理
- [ ] 设计用户设置系统
- [ ] 实现API密钥安全存储
- [ ] 创建系统初始化流程
- [ ] 实现配置导入导出功能
- [ ] 实现记忆窗口功能（窗口状态记忆）

#### 10.2.5 UI基础框架
- [ ] 实现Glassmorphism设计风格
- [ ] 创建SVG图标系统（禁用emoji）
- [ ] 实现明亮/暗黑主题切换
- [ ] 设计左右分区布局（40:60比例）
- [ ] 实现基础导航组件

### 10.3 第二阶段：AI服务与核心功能

#### 10.3.1 AI模型管理
- [ ] 实现OpenAI API集成
- [ ] 实现Claude API集成
- [ ] 实现Gemini API集成
- [ ] 实现ModelScope API集成
- [ ] 实现Ollama本地模型支持
- [ ] 实现SiliconFlow API集成
- [ ] 创建统一AI服务接口
- [ ] 实现智能API地址检测功能
- [ ] 实现统一API管理功能

#### 10.3.2 项目管理功能
- [ ] 实现项目创建和管理
- [ ] 设计项目文件结构
- [ ] 实现项目导入导出
- [ ] 创建项目模板系统
- [ ] 实现项目设置管理
- [ ] 实现自动备份系统

#### 10.3.3 大纲生成功能
- [ ] 实现大纲生成核心逻辑
- [ ] 创建内置提示词模板系统
- [ ] 实现参数化配置界面（章节数1-9999，字数300-9999）
- [ ] 实现分段生成功能
- [ ] 创建大纲预览功能
- [ ] 实现内置小说类型/主题/风格选项
- [ ] 实现网络小说平台适配

#### 10.3.4 大纲编辑功能
- [ ] 实现大纲编辑器界面
- [ ] 创建版本管理系统
- [ ] 实现AI辅助编辑功能
- [ ] 实现实时预览功能
- [ ] 创建大纲导入导出功能
- [ ] 实现多标签页编辑

#### 10.3.5 设置功能
- [ ] 实现设置界面
- [ ] 创建AI模型配置管理
- [ ] 实现界面主题设置
- [ ] 创建快捷键配置
- [ ] 实现数据管理功能
- [ ] 实现配置导入导出

### 10.4 第三阶段：章节创作与分析功能

#### 10.4.1 章节编辑功能
- [ ] 实现富文本编辑器
- [ ] 创建语法高亮功能
- [ ] 实现自动保存机制
- [ ] 创建专注模式
- [ ] 实现快捷键支持
- [ ] 实现选定文本润色功能
- [ ] 创建章节模板应用

#### 10.4.2 章节生成功能
- [ ] 实现章节内容生成
- [ ] 创建多种生成模式（完整生成/续写模式/分段生成）
- [ ] 实现上下文管理功能
- [ ] 创建质量控制机制
- [ ] 实现生成进度显示
- [ ] 实现降AI味功能
- [ ] 创建生成参数优化

#### 10.4.3 章节分析功能
- [ ] 实现章节分析算法
- [ ] 创建多维度分析报告（核心剧情分析、故事梗概提取、优缺点分析）
- [ ] 实现角色标注和物品标注
- [ ] 创建改进建议生成
- [ ] 实现可视化展示
- [ ] 创建对比分析功能

#### 10.4.4 人物编辑功能
- [ ] 实现角色信息管理
- [ ] 创建角色分类系统（主角、重要角色、配角、龙套）
- [ ] 实现AI辅助角色生成
- [ ] 创建角色模板系统
- [ ] 实现角色导入导出
- [ ] 实现角色一致性检查

#### 10.4.5 人物关系图功能
- [ ] 实现关系图可视化
- [ ] 创建关系类型管理
- [ ] 实现交互式操作
- [ ] 创建布局算法
- [ ] 实现关系分析功能
- [ ] 实现关系强度设置

#### 10.4.6 统计信息功能
- [ ] 实现基础统计功能（小说标题、章节数、总字数、平均每章字数、已完成章节、完成度）
- [ ] 创建可视化图表
- [ ] 实现实时数据更新
- [ ] 创建统计报告导出
- [ ] 实现趋势分析
- [ ] 实现章节详细统计表格

### 10.5 第四阶段：智能化高级功能

#### 10.5.1 AI聊天功能
- [ ] 实现AI对话界面
- [ ] 创建对话历史管理
- [ ] 实现角色扮演模式
- [ ] 创建快速提问模板
- [ ] 实现上下文记忆
- [ ] 实现多模型切换
- [ ] 创建对话验证功能

#### 10.5.2 提示词库功能
- [ ] 实现提示词分类管理
- [ ] 创建内置模板库（大纲生成、章节生成、人物生成、世界观生成、写作技巧、特殊功能）
- [ ] 实现自定义模板功能
- [ ] 创建模板质量控制
- [ ] 实现模板导入导出
- [ ] 实现模板变量系统

#### 10.5.3 上下文管理功能
- [ ] 实现上下文分类系统（项目级、章节级、人物级、场景级、自定义）
- [ ] 创建智能提取功能
- [ ] 实现关联推荐
- [ ] 创建优化建议
- [ ] 实现版本控制
- [ ] 实现上下文应用引擎

#### 10.5.4 向量库检索功能
- [ ] 集成ChromaDB向量数据库
- [ ] 实现语义检索功能
- [ ] 创建嵌入模型管理（支持多种嵌入模型）
- [ ] 实现检索结果排序
- [ ] 创建检索历史管理
- [ ] 实现混合检索功能
- [ ] 创建检索结果优化

#### 10.5.5 降AI味功能
- [ ] 实现降AI味算法
- [ ] 创建多种处理策略（语言多样化、情感增强、细节生动化、逻辑自然化）
- [ ] 实现批量处理功能
- [ ] 创建质量检测
- [ ] 实现效果评估
- [ ] 实现用户控制选项

### 10.6 第五阶段：系统优化与完善

#### 10.6.1 性能优化
- [ ] 优化数据库查询性能
- [ ] 实现内存管理优化
- [ ] 优化UI渲染性能
- [ ] 实现缓存机制
- [ ] 优化API调用效率
- [ ] 实现向量库性能优化
- [ ] 优化大文件处理

#### 10.6.2 用户体验改进
- [ ] 完善Glassmorphism设计风格
- [ ] 实现明亮/暗黑主题切换功能
- [ ] 优化交互体验
- [ ] 实现快捷键系统
- [ ] 创建用户引导功能
- [ ] 完善SVG图标系统
- [ ] 优化界面响应速度

#### 10.6.3 系统完善
- [ ] 完善记忆窗口功能
- [ ] 优化应用运行日志系统
- [ ] 完善智能API地址检测
- [ ] 优化统一API管理
- [ ] 完善配置加密存储
- [ ] 实现多显示器支持

#### 10.6.4 质量保证
- [ ] 完善单元测试
- [ ] 实现集成测试
- [ ] 进行性能测试
- [ ] 实现错误监控
- [ ] 创建质量报告
- [ ] 验证所有14个功能模块
- [ ] 测试网络小说平台适配

#### 10.6.5 打包部署
- [ ] 配置Tauri打包
- [ ] 创建Inno Setup安装程序
- [ ] 实现自动更新
- [ ] 优化应用体积
- [ ] 创建部署文档
- [ ] 配置数字签名
- [ ] 创建多平台安装包

### 10.7 功能完整性检查清单

#### 10.7.1 核心功能模块（14个）
- [ ] 大纲生成功能
- [ ] 大纲编辑功能
- [ ] 章节编辑功能
- [ ] 章节生成功能
- [ ] 章节分析功能
- [ ] 人物编辑功能
- [ ] 人物关系图功能
- [ ] 统计信息功能
- [ ] AI聊天功能
- [ ] 提示词库功能
- [ ] 上下文管理功能
- [ ] 向量库检索功能
- [ ] 降AI味功能
- [ ] 设置功能

#### 10.7.2 特殊功能要求
- [ ] 智能API地址检测功能
- [ ] 统一API管理功能
- [ ] 记忆窗口功能
- [ ] 应用运行日志系统
- [ ] Glassmorphism UI设计风格
- [ ] SVG图标系统（禁用emoji）
- [ ] .ainovel文件格式支持
- [ ] 网络小说平台适配
- [ ] 内置选项配置（类型/主题/风格）
- [ ] 参数限制（章节数1-9999，字数300-9999）

#### 10.7.3 AI模型支持（全面开放）
- [ ] OpenAI全系列模型支持（GPT-4o、GPT-4、GPT-3.5等所有模型）
- [ ] Claude全系列模型支持（Claude-3、Claude-2、Claude-Instant等所有模型）
- [ ] Gemini全系列模型支持（Gemini-2.0、Gemini-1.5、Gemini-1.0等所有模型）
- [ ] ModelScope平台所有模型支持（DeepSeek、Qwen、ChatGLM、Baichuan等）
- [ ] SiliconFlow平台所有模型支持（DeepSeek、Qwen、Llama、Mistral等）
- [ ] Ollama生态系统所有模型支持（用户可自由下载和使用任意模型）
- [ ] 自定义OpenAI兼容API支持（支持任何兼容接口的模型服务）
- [ ] 动态模型选择和切换功能
- [ ] 模型可用性自动检测功能

## 11. 环境搭建指南

### 11.1 开发环境要求

#### 11.1.1 系统要求
- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**：最低8GB，推荐16GB
- **存储空间**：至少10GB可用空间
- **网络**：稳定的互联网连接（用于AI API调用）

#### 11.1.2 开发工具
- **代码编辑器**：VS Code（推荐）或其他支持Rust/Python/Vue的IDE
- **版本控制**：Git 2.30+
- **包管理器**：Node.js 18+ (npm/yarn), Python 3.11+, Rust 1.70+

### 11.2 环境安装步骤

#### 11.2.1 安装Rust和Tauri
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 安装Tauri CLI
cargo install tauri-cli

# 验证安装
cargo tauri --version
```

#### 11.2.2 安装Node.js和前端依赖
```bash
# 安装Node.js (推荐使用nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装前端依赖
npm install
# 或使用yarn
yarn install
```

#### 11.2.3 安装Python和后端依赖
```bash
# 安装Python 3.11+
# Windows: 从官网下载安装包
# macOS: brew install python@3.11
# Ubuntu: sudo apt install python3.11

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装Python依赖
pip install -r python-backend/requirements.txt
```

### 11.3 项目配置

#### 11.3.1 Tauri配置
```json
// tauri.conf.json
{
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devPath": "http://localhost:1420",
    "distDir": "../dist"
  },
  "package": {
    "productName": "AI小说助手",
    "version": "2.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "dialog": {
        "all": false,
        "open": true,
        "save": true
      },
      "fs": {
        "all": false,
        "readFile": true,
        "writeFile": true,
        "createDir": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.ainovel.assistant",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "AI小说助手",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600
      }
    ]
  }
}
```

#### 11.3.2 Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  clearScreen: false,
  server: {
    port: 1420,
    strictPort: true,
    watch: {
      ignored: ["**/src-tauri/**"]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    target: process.env.TAURI_PLATFORM == 'windows' ? 'chrome105' : 'safari13',
    minify: !process.env.TAURI_DEBUG ? 'esbuild' : false,
    sourcemap: !!process.env.TAURI_DEBUG
  }
})
```

### 11.4 开发调试

#### 11.4.1 启动开发服务器
```bash
# 启动前端开发服务器
npm run dev

# 启动Python后端服务器
cd python-backend
python app/main.py

# 启动Tauri开发模式
cargo tauri dev
```

#### 11.4.2 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Tauri Development Debug",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/ai-novel-assistant",
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "name": "Python Backend Debug",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/python-backend/app/main.py",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/python-backend"
    }
  ]
}
```

---

## 12. 打包部署方案

### 12.1 构建配置

#### 12.1.1 生产环境构建
```bash
# 构建前端资源
npm run build

# 构建Tauri应用
cargo tauri build
```

#### 12.1.2 Python后端打包
```python
# pyinstaller配置文件 build.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['python-backend/app/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('python-backend/app/templates', 'templates'),
        ('python-backend/app/static', 'static'),
    ],
    hiddenimports=[
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'openai',
        'anthropic',
        'google.generativeai'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ai-novel-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
```

### 12.2 安装程序制作

#### 12.2.1 Inno Setup配置
```ini
; setup.iss
[Setup]
AppName=AI小说助手
AppVersion=2.0.0
AppPublisher=AI Novel Assistant Team
AppPublisherURL=https://github.com/ai-novel-assistant
AppSupportURL=https://github.com/ai-novel-assistant/issues
AppUpdatesURL=https://github.com/ai-novel-assistant/releases
DefaultDirName={autopf}\AI小说助手
DefaultGroupName=AI小说助手
AllowNoIcons=yes
LicenseFile=LICENSE.txt
OutputDir=dist
OutputBaseFilename=AI小说助手-v2.0.0-setup
SetupIconFile=icons\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "src-tauri\target\release\ai-novel-assistant.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "python-backend\dist\ai-novel-backend.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\AI小说助手"; Filename: "{app}\ai-novel-assistant.exe"
Name: "{group}\{cm:UninstallProgram,AI小说助手}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\AI小说助手"; Filename: "{app}\ai-novel-assistant.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\ai-novel-assistant.exe"; Description: "{cm:LaunchProgram,AI小说助手}"; Flags: nowait postinstall skipifsilent
```

#### 12.2.2 MSI安装包配置
```xml
<!-- Product.wxs -->
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" Name="AI小说助手" Language="2052" Version="2.0.0"
           Manufacturer="AI Novel Assistant Team" UpgradeCode="12345678-1234-1234-1234-123456789012">

    <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />

    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />

    <MediaTemplate EmbedCab="yes" />

    <Feature Id="ProductFeature" Title="AI小说助手" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>

    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="AI小说助手" />
      </Directory>
    </Directory>

    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable" Guid="*">
        <File Id="MainExe" Source="src-tauri\target\release\ai-novel-assistant.exe" KeyPath="yes" />
      </Component>
      <Component Id="BackendExecutable" Guid="*">
        <File Id="BackendExe" Source="python-backend\dist\ai-novel-backend.exe" />
      </Component>
    </ComponentGroup>

  </Product>
</Wix>
```

### 12.3 自动化构建

#### 12.3.1 GitHub Actions配置
```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    strategy:
      matrix:
        platform: [windows-latest, macos-latest, ubuntu-20.04]

    runs-on: ${{ matrix.platform }}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          npm install
          pip install -r python-backend/requirements.txt
          pip install pyinstaller

      - name: Build Python backend
        run: |
          pyinstaller build.spec

      - name: Build Tauri app
        run: |
          npm run tauri build

      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.platform }}-build
          path: src-tauri/target/release/bundle/
```

### 12.4 分发策略

#### 12.4.1 版本管理
- **语义化版本**：采用MAJOR.MINOR.PATCH格式
- **发布渠道**：GitHub Releases作为主要分发渠道
- **更新检查**：内置自动更新检查功能
- **增量更新**：支持增量更新减少下载量

#### 12.4.2 平台适配
- **Windows**：提供MSI和EXE安装包
- **macOS**：提供DMG和PKG安装包
- **Linux**：提供AppImage、DEB、RPM包

#### 12.4.3 数字签名
```bash
# Windows代码签名
signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com ai-novel-assistant.exe

# macOS代码签名
codesign --force --options runtime --sign "Developer ID Application: Your Name" ai-novel-assistant.app
```

## 13. 创作流程指南

### 13.1 完整创作流程

AI小说助手提供了一套完整的小说创作流程，从构思到完成，每个步骤都有AI辅助支持。

```mermaid
flowchart TD
    A[项目创建] --> B[大纲生成]
    B --> C[大纲编辑]
    C --> D[人物设计]
    D --> E[章节创作]
    E --> F[章节分析]
    F --> G[内容优化]
    G --> H[统计检查]
    H --> I[项目完成]

    E --> J[章节生成]
    J --> K[章节编辑]
    K --> E

    F --> L[AI聊天咨询]
    L --> G
```

### 13.2 详细操作步骤

#### 13.2.1 第一步：项目创建
1. **启动应用**
   - 打开AI小说助手
   - 点击"新建项目"按钮

2. **基本信息设置**
   - 输入项目名称
   - 选择小说类型（玄幻、都市、历史等）
   - 设定目标章节数和每章字数
   - 选择创作风格

3. **AI模型配置**
   - 在设置中配置AI模型API
   - 测试API连接状态
   - 选择默认使用的AI模型

#### 13.2.2 第二步：大纲生成
1. **进入大纲生成界面**
   - 点击左侧导航"大纲生成"
   - 选择AI模型和提示词模板

2. **设置生成参数**
   - 填写小说标题、类型、主题、风格
   - 设置章节数和每章字数
   - 配置人物数量（主角、重要角色、配角、龙套）
   - 选择生成范围（起始章节到结束章节）

3. **生成大纲**
   - 点击"生成大纲"按钮
   - 等待AI生成完整大纲
   - 查看生成结果，包括：
     - 小说标题和核心主题
     - 主要人物设定
     - 故事梗概
     - 章节结构
     - 世界观设定

#### 13.2.3 第三步：大纲编辑
1. **进入大纲编辑界面**
   - 点击左侧导航"大纲编辑"
   - 查看当前大纲版本

2. **完善基本信息**
   - 在"基本信息"标签页中：
     - 优化小说标题（可使用AI生成）
     - 完善中心思想（可使用AI生成）
     - 调整小说类型和风格
     - 确认章节数和字数设置

3. **扩展故事梗概**
   - 在"故事梗概"标签页中：
     - 详细描述故事主线
     - 添加重要情节点
     - 使用AI辅助扩展内容

4. **丰富世界观设定**
   - 在"世界观"标签页中：
     - 完善背景设定
     - 建立规则体系
     - 描述文化社会环境

5. **优化章节大纲**
   - 在"章节大纲"标签页中：
     - 调整章节标题
     - 完善章节摘要
     - 确保情节连贯性

#### 13.2.4 第四步：人物设计
1. **进入人物编辑界面**
   - 点击左侧导航"人物编辑"
   - 查看从大纲生成的角色列表

2. **完善主要角色**
   - 选择主角进行编辑：
     - 基本信息：姓名、性别、年龄、职业
     - 外貌特征：身高、体重、外貌描述
     - 性格特点：性格描述、行为习惯
     - 背景经历：出身背景、重要经历
     - 能力技能：特殊能力、技能等级

3. **设计角色关系**
   - 进入"人物关系图"界面
   - 添加角色间的关系：
     - 选择两个角色
     - 设定关系类型（亲情、爱情、友情、敌对等）
     - 设置关系强度
     - 添加关系描述

4. **AI辅助角色生成**
   - 使用AI生成新角色
   - 完善角色设定
   - 生成角色背景故事

#### 13.2.5 第五步：章节创作

**方式一：章节生成**
1. **进入章节生成界面**
   - 点击左侧导航"章节生成"
   - 选择要生成的章节

2. **配置生成设置**
   - 选择AI模型
   - 设置目标字数
   - 选择生成模式（完整生成/续写模式/分段生成）

3. **设置上下文**
   - 勾选包含的上下文信息：
     - 大纲信息
     - 前章内容
     - 角色信息
     - 世界观设定
   - 设置上下文长度

4. **生成章节内容**
   - 点击"开始生成"
   - 监控生成进度
   - 查看生成结果

**方式二：章节编辑**
1. **进入章节编辑界面**
   - 点击左侧导航"章节编辑"
   - 选择要编辑的章节

2. **手动创作**
   - 输入章节标题
   - 编写章节摘要
   - 在编辑器中创作内容

3. **AI辅助编辑**
   - 选中文本使用AI润色
   - 使用AI扩展内容
   - 应用章节模板

#### 13.2.6 第六步：章节分析与优化
1. **进入章节分析界面**
   - 点击左侧导航"章节分析"
   - 选择要分析的章节

2. **设置分析选项**
   - 勾选分析维度：
     - 核心剧情分析
     - 故事梗概提取
     - 优缺点分析
     - 角色标注
     - 物品标注
     - 改进建议

3. **执行分析**
   - 选择分析模型
   - 设置分析深度
   - 点击"开始分析"

4. **查看分析报告**
   - 阅读分析结果
   - 查看改进建议
   - 应用优化建议

5. **章节改进**
   - 根据分析建议修改章节
   - 使用AI辅助改进功能
   - 重新分析验证效果

#### 13.2.7 第七步：AI聊天咨询
1. **进入AI聊天界面**
   - 点击左侧导航"AI聊天"
   - 选择AI模型

2. **咨询写作问题**
   - 询问剧情发展建议
   - 讨论角色塑造技巧
   - 咨询写作技法
   - 解决创作困惑

3. **使用快速提问**
   - 选择提问模板
   - 快速获取专业建议

#### 13.2.8 第八步：统计检查
1. **进入统计信息界面**
   - 点击左侧导航"统计信息"
   - 查看创作进度

2. **检查项目状态**
   - 总体概览：章节数、总字数、完成度
   - 章节统计：各章节字数和状态
   - 人物统计：角色出场频率
   - 进度统计：创作趋势分析

3. **导出统计报告**
   - 选择导出格式
   - 生成统计报告
   - 保存或打印报告

### 13.3 高级功能使用

#### 13.3.1 提示词库管理
1. **使用内置模板**
   - 浏览提示词分类
   - 选择合适的模板
   - 应用到创作中

2. **创建自定义模板**
   - 新建提示词模板
   - 设置模板变量
   - 保存和分类管理

#### 13.3.2 上下文管理
1. **创建上下文信息**
   - 添加项目级上下文
   - 记录章节级信息
   - 管理人物级背景

2. **智能上下文提取**
   - 自动提取重要信息
   - 建立信息关联
   - 优化上下文结构

#### 13.3.3 向量库检索
1. **内容检索**
   - 输入检索关键词
   - 设置检索参数
   - 查看相似内容

2. **参考素材管理**
   - 收集创作素材
   - 建立素材库
   - 快速查找参考

### 13.4 项目管理最佳实践

#### 13.4.1 版本控制
- 定期保存项目版本
- 重要修改前创建备份
- 使用版本标签管理里程碑

#### 13.4.2 协作创作
- 导出项目文件分享
- 使用统一的创作规范
- 定期同步项目进度

#### 13.4.3 质量保证
- 定期进行章节分析
- 保持角色一致性
- 检查情节逻辑性
- 使用降AI味功能优化内容

#### 13.4.4 效率提升
- 熟练使用快捷键
- 建立个人模板库
- 优化AI模型配置
- 合理安排创作时间

### 13.5 常见问题解决

#### 13.5.1 AI生成质量问题
- **问题**：生成内容质量不佳
- **解决方案**：
  - 优化提示词模板
  - 调整AI模型参数
  - 增加上下文信息
  - 使用降AI味功能

#### 13.5.2 角色一致性问题
- **问题**：角色前后表现不一致
- **解决方案**：
  - 完善角色设定
  - 使用角色模板
  - 定期检查角色描述
  - 利用上下文管理

#### 13.5.3 情节连贯性问题
- **问题**：章节间情节不连贯
- **解决方案**：
  - 加强大纲规划
  - 使用章节分析功能
  - 维护情节线索
  - 定期回顾前文

#### 13.5.4 创作效率问题
- **问题**：创作速度慢
- **解决方案**：
  - 使用AI辅助生成
  - 建立个人模板库
  - 优化工作流程
  - 合理分配创作任务

---

## 结语

AI小说助手是一款专为网络小说创作者设计的智能写作工具，通过集成多种AI模型和智能化功能，为创作者提供从构思到完成的全流程支持。

本开发文档详细描述了应用的技术架构、功能设计、界面布局和实现方案，为开发团队提供了完整的开发指南。文档严格按照开发计划4的要求编写，确保了技术方案的可行性和功能的完整性。

希望这款工具能够帮助更多的创作者实现他们的文学梦想，创作出更多优秀的网络小说作品。

---

**文档版本**：V2.0
**最后更新**：[日期]
**文档状态**：完整版
**技术栈**：Tauri + Python + Vue 3
**设计风格**：Glassmorphism

---