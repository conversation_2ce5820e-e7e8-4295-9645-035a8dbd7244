<execution>
  <constraint>
    ## 创作质量约束
    - **字数控制**：章节字数300-9999字，确保阅读体验
    - **更新频率**：支持日更、周更等不同更新节奏
    - **内容审核**：符合各大平台的内容审核标准
    - **版权保护**：确保生成内容的原创性，避免抄袭风险
    - **AI模型限制**：需要处理不同AI模型的token限制和响应时间
  </constraint>

  <rule>
    ## 创作流程强制规则
    - **大纲先行**：必须先完成大纲设计再进行章节创作
    - **角色一致性**：角色性格和行为必须与设定保持一致
    - **情节逻辑性**：故事发展必须符合基本逻辑，避免明显漏洞
    - **上下文连贯**：新生成内容必须与已有内容保持连贯
    - **质量检查**：每个章节完成后必须进行质量评估
    - **版本管理**：重要修改必须保留版本历史，支持回滚
  </rule>

  <guideline>
    ## 创作指导原则
    - **读者体验优先**：始终以提升读者阅读体验为核心目标
    - **创意与商业平衡**：在保持创意的同时考虑商业价值
    - **渐进式完善**：通过多轮迭代逐步提升内容质量
    - **个性化适配**：根据作者风格和读者偏好调整创作策略
    - **数据驱动优化**：基于阅读数据和反馈持续优化
    - **可持续创作**：建立可长期维持的创作节奏和质量标准
  </guideline>

  <process>
    ## 完整创作流程
    
    ### Step 1: 项目规划与设定
    ```mermaid
    flowchart TD
        A[创作灵感] --> B[基本设定确定]
        B --> C[类型和风格选择]
        C --> D[目标读者分析]
        D --> E[商业模式规划]
        E --> F[创作计划制定]
    ```
    
    **具体操作**：
    1. 确定小说的核心主题和创意点
    2. 选择小说类型（玄幻、都市、历史等）
    3. 定义目标读者群体和平台
    4. 制定章节数量和更新计划
    5. 设置创作目标和里程碑
    
    ### Step 2: 大纲生成与完善
    ```mermaid
    graph LR
        A[AI大纲生成] --> B[人工审核修改]
        B --> C[细节补充完善]
        C --> D[逻辑一致性检查]
        D --> E[最终大纲确认]
    ```
    
    **大纲生成流程**：
    1. **基础信息输入**：标题、类型、主题、风格、字数等
    2. **AI智能生成**：使用专业提示词模板生成初版大纲
    3. **结构优化**：调整章节结构和情节发展节奏
    4. **角色完善**：丰富主要角色的设定和关系网络
    5. **质量评估**：对大纲进行多维度质量评估
    
    ### Step 3: 角色设计与管理
    ```mermaid
    mindmap
      root((角色管理))
        主要角色
          主角设定
          重要配角
          反派角色
        角色属性
          基本信息
          性格特征
          背景故事
          能力技能
        关系网络
          亲情关系
          爱情关系
          友情关系
          敌对关系
        成长轨迹
          初始状态
          发展过程
          最终形态
    ```
    
    ### Step 4: 章节创作流程
    ```mermaid
    flowchart TD
        A[章节规划] --> B[上下文准备]
        B --> C[AI内容生成]
        C --> D[人工编辑优化]
        D --> E[质量检查]
        E --> F{质量达标?}
        F -->|是| G[章节完成]
        F -->|否| H[重新生成/修改]
        H --> C
    ```
    
    **章节创作详细步骤**：
    1. **章节目标设定**：明确本章要达成的情节目标
    2. **上下文加载**：加载相关的角色、情节、设定信息
    3. **提示词构建**：根据章节类型选择合适的提示词模板
    4. **AI内容生成**：调用AI模型生成初版章节内容
    5. **降AI味处理**：优化语言表达，增强内容自然度
    6. **逻辑检查**：验证情节逻辑和角色行为的合理性
    7. **风格统一**：确保与全书风格保持一致
    8. **质量评估**：进行多维度质量评分
    
    ### Step 5: 内容优化与完善
    ```mermaid
    graph TD
        A[初版内容] --> B[语言优化]
        B --> C[情节完善]
        C --> D[角色深化]
        D --> E[细节丰富]
        E --> F[节奏调整]
        F --> G[最终版本]
    ```
    
    **优化重点**：
    - **语言表达**：提升文字的流畅性和表现力
    - **情节发展**：确保情节推进的合理性和吸引力
    - **角色塑造**：深化角色性格和情感表达
    - **环境描写**：丰富场景描述和氛围营造
    - **对话设计**：优化人物对话的真实性和个性化
    
    ### Step 6: 质量评估与发布
    ```mermaid
    flowchart LR
        A[内容完成] --> B[自动质量检测]
        B --> C[人工最终审核]
        C --> D[格式化处理]
        D --> E[平台发布]
        E --> F[读者反馈收集]
        F --> G[后续优化]
    ```
    
    **质量评估维度**：
    - **可读性评分**：语言流畅度和易读性
    - **逻辑性评分**：情节发展的逻辑合理性
    - **创意性评分**：内容的新颖性和创意度
    - **商业性评分**：商业价值和市场潜力
    - **技术指标**：字数、段落数、对话比例等
  </process>

  <criteria>
    ## 创作质量评价标准
    
    ### 内容质量标准
    - ✅ 情节发展逻辑合理，无明显漏洞
    - ✅ 角色性格鲜明，行为符合设定
    - ✅ 语言表达流畅自然，无AI痕迹
    - ✅ 节奏控制得当，张弛有度
    
    ### 技术指标标准
    - ✅ 章节字数符合设定范围
    - ✅ 更新频率满足计划要求
    - ✅ 错别字率 < 0.1%
    - ✅ 重复内容率 < 5%
    
    ### 商业价值标准
    - ✅ 符合目标平台的内容要求
    - ✅ 具备良好的商业化潜力
    - ✅ 读者接受度和满意度高
    - ✅ 具备IP开发价值
    
    ### 用户体验标准
    - ✅ 创作流程简单高效
    - ✅ AI辅助功能实用有效
    - ✅ 界面操作直观便捷
    - ✅ 学习成本低，上手快
  </criteria>
</execution>
