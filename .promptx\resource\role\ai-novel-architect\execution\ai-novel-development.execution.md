<execution>
  <constraint>
    ## 项目技术约束
    - **Tauri 2.0版本要求**：必须使用Tauri 2.0以上版本，确保最新特性支持
    - **Python 3.11+要求**：后端服务必须基于Python 3.11以上版本
    - **应用体积限制**：最终打包体积不得超过50MB
    - **内存使用限制**：运行时内存占用不得超过512MB
    - **响应时间要求**：UI操作响应时间不得超过200ms
    - **AI API限制**：需要处理各厂商的速率限制和token限制
  </constraint>

  <rule>
    ## 强制性开发规则
    - **零配置原则**：所有依赖必须内置打包，用户无需额外安装
    - **数据本地化**：所有用户数据必须本地存储，不得上传云端
    - **API密钥安全**：用户API密钥必须加密存储，不得明文保存
    - **错误处理完整性**：所有AI API调用必须有完整的错误处理和重试机制
    - **跨平台兼容性**：代码必须在Windows、macOS、Linux三平台正常运行
    - **版本向后兼容**：新版本必须能正确读取旧版本的项目文件
  </rule>

  <guideline>
    ## 开发指导原则
    - **用户体验优先**：所有技术决策以提升用户体验为首要目标
    - **渐进式开发**：先实现核心功能，再逐步添加高级特性
    - **模块化设计**：各功能模块独立开发，便于维护和扩展
    - **性能优化意识**：在开发过程中持续关注性能表现
    - **代码质量保证**：遵循代码规范，保持良好的可读性和可维护性
    - **测试驱动**：重要功能必须有对应的单元测试和集成测试
  </guideline>

  <process>
    ## 完整开发流程
    
    ### Step 1: 环境搭建与项目初始化
    ```mermaid
    flowchart TD
        A[开发环境准备] --> B[Tauri项目创建]
        B --> C[Vue3前端初始化]
        C --> D[Python后端搭建]
        D --> E[SQLite数据库设计]
        E --> F[基础架构验证]
    ```
    
    **具体操作**：
    1. 安装Rust、Node.js、Python开发环境
    2. 使用`cargo tauri init`创建项目骨架
    3. 配置Vue3 + TypeScript + Vite前端环境
    4. 搭建FastAPI后端服务框架
    5. 设计并创建SQLite数据库表结构
    
    ### Step 2: 核心功能模块开发
    ```mermaid
    graph LR
        A[AI服务层] --> B[大纲生成]
        A --> C[章节编辑]
        A --> D[角色管理]
        A --> E[上下文系统]
        
        B --> F[用户界面]
        C --> F
        D --> F
        E --> F
    ```
    
    **开发优先级**：
    1. **AI服务抽象层**：统一的AI模型调用接口
    2. **大纲生成模块**：基于AI的智能大纲创建
    3. **章节编辑器**：富文本编辑+AI辅助功能
    4. **角色管理系统**：角色档案+关系图
    5. **上下文管理**：智能上下文提取和应用
    
    ### Step 3: UI/UX设计与实现
    ```mermaid
    flowchart TD
        A[Glassmorphism设计系统] --> B[组件库开发]
        B --> C[页面布局实现]
        C --> D[交互逻辑开发]
        D --> E[响应式适配]
        E --> F[主题切换功能]
    ```
    
    **设计要点**：
    - 实现毛玻璃效果的视觉设计
    - 确保界面在不同分辨率下的适配
    - 优化交互流程，减少用户操作步骤
    - 支持明暗主题无缝切换
    
    ### Step 4: 高级功能集成
    ```mermaid
    mindmap
      root((高级功能))
        提示词系统
          内置模板库
          自定义模板
          变量替换
        统计分析
          创作进度
          质量评估
          数据可视化
        导入导出
          多格式支持
          平台适配
          数据迁移
    ```
    
    ### Step 5: 测试与优化
    ```mermaid
    flowchart LR
        A[单元测试] --> B[集成测试]
        B --> C[性能测试]
        C --> D[用户体验测试]
        D --> E[跨平台测试]
        E --> F[优化迭代]
    ```
    
    **测试重点**：
    - AI API调用的稳定性和错误处理
    - 大文件处理的性能表现
    - 跨平台的兼容性验证
    - 用户界面的响应速度
    
    ### Step 6: 打包部署
    ```mermaid
    graph TD
        A[代码构建] --> B[Python后端打包]
        B --> C[Tauri应用构建]
        C --> D[多平台打包]
        D --> E[数字签名]
        E --> F[发布分发]
    ```
    
    **部署要点**：
    - 使用PyInstaller打包Python后端为单文件
    - 配置Tauri构建参数优化应用体积
    - 为不同平台生成对应的安装包格式
    - 添加数字签名确保应用安全性
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ 大纲生成功能完全可用
    - ✅ 章节编辑器功能齐全
    - ✅ AI模型集成稳定
    - ✅ 数据导入导出正常
    
    ### 性能指标
    - ✅ 应用启动时间 < 3秒
    - ✅ UI操作响应时间 < 200ms
    - ✅ 内存占用 < 512MB
    - ✅ 应用体积 < 50MB
    
    ### 用户体验
    - ✅ 界面美观现代
    - ✅ 操作流程直观
    - ✅ 错误提示友好
    - ✅ 学习成本低
    
    ### 技术质量
    - ✅ 代码规范性良好
    - ✅ 测试覆盖率 > 80%
    - ✅ 跨平台兼容性完整
    - ✅ 安全性措施到位
  </criteria>
</execution>
