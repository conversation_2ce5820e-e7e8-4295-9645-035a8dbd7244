<execution>
  <constraint>
    ## 质量保证客观约束
    - **性能硬性指标**：应用体积<50MB，内存<512MB，响应<200ms
    - **功能完整性要求**：14个核心功能模块必须完整实现
    - **兼容性要求**：支持Windows、macOS、Linux三大平台
    - **安全性要求**：API密钥加密存储，用户数据隐私保护
    - **可用性要求**：界面美观，操作流畅，学习成本低
  </constraint>

  <rule>
    ## 质量保证强制规则
    - **测试先行**：每个功能开发前必须先设计测试用例
    - **代码审查**：关键代码变更必须经过同行审查
    - **自动化测试**：核心功能必须有自动化测试覆盖
    - **性能监控**：持续监控应用性能指标
    - **用户反馈**：建立用户反馈收集和处理机制
  </rule>

  <guideline>
    ## 质量保证指导原则
    - **预防优于修复**：在设计阶段就考虑质量问题
    - **持续改进**：基于测试结果和用户反馈持续优化
    - **全员质量**：质量是每个团队成员的责任
    - **数据驱动**：基于客观数据评估和改进质量
    - **用户中心**：以用户体验为质量评判的最终标准
  </guideline>

  <process>
    ## 质量保证执行流程
    
    ### 质量计划制定
    ```mermaid
    flowchart TD
        A[需求分析] --> B[质量目标设定]
        B --> C[质量标准制定]
        C --> D[测试策略设计]
        D --> E[质量流程设计]
        E --> F[质量工具选择]
        F --> G[质量计划确认]
    ```
    
    ### 开发阶段质量控制
    ```mermaid
    graph LR
        A[需求评审] --> B[设计评审]
        B --> C[代码开发]
        C --> D[单元测试]
        D --> E[代码审查]
        E --> F[集成测试]
        F --> G[系统测试]
        G --> H[验收测试]
    ```
    
    ### 质量门控检查
    ```mermaid
    flowchart TD
        A[功能完成] --> B{单元测试通过?}
        B -->|否| C[修复问题]
        B -->|是| D{代码审查通过?}
        D -->|否| C
        D -->|是| E{集成测试通过?}
        E -->|否| C
        E -->|是| F{性能测试通过?}
        F -->|否| C
        F -->|是| G{用户体验测试通过?}
        G -->|否| C
        G -->|是| H[质量门控通过]
        C --> A
    ```
    
    ### 缺陷管理流程
    ```mermaid
    graph TD
        A[缺陷发现] --> B[缺陷记录]
        B --> C[缺陷分类]
        C --> D[优先级评估]
        D --> E[分配修复]
        E --> F[修复验证]
        F --> G{修复成功?}
        G -->|是| H[缺陷关闭]
        G -->|否| I[重新修复]
        I --> F
    ```
    
    ### 质量度量分析
    ```mermaid
    flowchart LR
        A[数据收集] --> B[指标计算]
        B --> C[趋势分析]
        C --> D[问题识别]
        D --> E[改进建议]
        E --> F[改进实施]
        F --> G[效果验证]
        G --> A
    ```
  </process>

  <criteria>
    ## 质量保证评价标准
    
    ### 功能质量指标
    - ✅ 功能完整性 = 100%（14个核心功能全部实现）
    - ✅ 功能正确性 ≥ 99%（关键功能零缺陷）
    - ✅ 功能可用性 ≥ 95%（用户可正常使用）
    - ✅ 功能稳定性 ≥ 99.5%（无崩溃和严重错误）
    
    ### 性能质量指标
    - ✅ 应用启动时间 ≤ 3秒
    - ✅ 界面响应时间 ≤ 200ms
    - ✅ 内存使用量 ≤ 512MB
    - ✅ 应用安装包大小 ≤ 50MB
    - ✅ CPU使用率 ≤ 30%（正常使用）
    
    ### 用户体验指标
    - ✅ 界面美观度 ≥ 4.0/5.0
    - ✅ 操作便捷性 ≥ 4.0/5.0
    - ✅ 学习成本 ≤ 30分钟（新用户上手）
    - ✅ 用户满意度 ≥ 85%
    - ✅ 用户推荐度 ≥ 70%
    
    ### 技术质量指标
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 代码复杂度 ≤ 10（圈复杂度）
    - ✅ 代码重复率 ≤ 5%
    - ✅ 技术债务 ≤ 1天（修复时间）
    - ✅ 安全漏洞 = 0个（高危和中危）
  </criteria>
</execution>
