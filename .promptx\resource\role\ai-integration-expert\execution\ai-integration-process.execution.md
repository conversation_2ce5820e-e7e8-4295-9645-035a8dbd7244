<execution>
  <constraint>
    ## AI集成技术约束
    - **API限制**：各厂商的速率限制、token限制、并发限制
    - **网络延迟**：国际API的网络延迟和稳定性问题
    - **成本控制**：API调用成本的预算限制和监控要求
    - **安全合规**：API密钥安全、数据隐私、审计要求
    - **性能要求**：响应时间不超过30秒，成功率不低于95%
    - **兼容性**：支持不同版本API的向后兼容
  </constraint>

  <rule>
    ## AI集成强制规则
    - **密钥安全**：API密钥必须加密存储，禁止明文保存
    - **错误处理**：所有API调用必须有完整的错误处理机制
    - **超时控制**：每个API调用必须设置合理的超时时间
    - **重试机制**：失败请求必须有智能重试策略
    - **日志记录**：所有API调用必须记录完整的审计日志
    - **资源限制**：必须实现API调用的频率限制和配额管理
  </rule>

  <guideline>
    ## AI集成指导原则
    - **用户体验优先**：确保AI功能的稳定性和响应速度
    - **成本效益平衡**：在质量和成本之间找到最佳平衡点
    - **渐进式集成**：先集成核心模型，再逐步扩展
    - **监控驱动**：基于监控数据持续优化集成策略
    - **文档完善**：维护完整的API集成文档和最佳实践
    - **测试充分**：每个集成模型都必须经过充分测试
  </guideline>

  <process>
    ## 完整AI集成流程
    
    ### Step 1: 统一抽象层设计
    ```mermaid
    flowchart TD
        A[定义统一接口] --> B[设计数据模型]
        B --> C[错误处理规范]
        C --> D[响应格式标准]
        D --> E[性能监控接口]
    ```
    
    **核心接口设计**：
    ```python
    from abc import ABC, abstractmethod
    from typing import Dict, List, Optional, Any
    
    class AIModelAdapter(ABC):
        """AI模型适配器基类"""
        
        @abstractmethod
        async def generate_text(
            self, 
            prompt: str, 
            max_tokens: int = 2000,
            temperature: float = 0.7,
            **kwargs
        ) -> Dict[str, Any]:
            """文本生成统一接口"""
            pass
        
        @abstractmethod
        async def check_health(self) -> bool:
            """健康检查接口"""
            pass
        
        @abstractmethod
        def get_model_info(self) -> Dict[str, Any]:
            """获取模型信息"""
            pass
    ```
    
    ### Step 2: 厂商适配器实现
    ```mermaid
    graph LR
        A[OpenAI适配器] --> E[统一接口]
        B[Claude适配器] --> E
        C[Gemini适配器] --> E
        D[Ollama适配器] --> E
        
        E --> F[AI服务管理器]
    ```
    
    **OpenAI适配器示例**：
    ```python
    class OpenAIAdapter(AIModelAdapter):
        def __init__(self, api_key: str, model: str = "gpt-4o"):
            self.client = OpenAI(api_key=api_key)
            self.model = model
        
        async def generate_text(
            self, 
            prompt: str, 
            max_tokens: int = 2000,
            temperature: float = 0.7,
            **kwargs
        ) -> Dict[str, Any]:
            try:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_tokens,
                    temperature=temperature
                )
                
                return {
                    "success": True,
                    "content": response.choices[0].message.content,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "model": self.model,
                    "provider": "openai"
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "provider": "openai"
                }
    ```
    
    ### Step 3: 智能路由系统
    ```mermaid
    flowchart TD
        A[任务分析] --> B[模型能力匹配]
        B --> C[成本效益评估]
        C --> D[性能预测]
        D --> E[综合决策]
        E --> F[模型选择]
    ```
    
    **路由决策算法**：
    ```python
    class AIRouter:
        def __init__(self):
            self.model_capabilities = {
                "gpt-4o": {"creativity": 0.95, "logic": 0.9, "cost": 0.3},
                "gpt-4": {"creativity": 0.9, "logic": 0.95, "cost": 0.5},
                "claude-3": {"creativity": 0.85, "logic": 0.9, "cost": 0.6},
                "gemini-2.0": {"creativity": 0.8, "logic": 0.85, "cost": 0.7}
            }
        
        def select_model(
            self, 
            task_type: str, 
            priority: str = "balanced",
            budget_limit: float = None
        ) -> str:
            """智能选择最适合的模型"""
            
            # 任务类型权重
            task_weights = {
                "creative": {"creativity": 0.7, "logic": 0.2, "cost": 0.1},
                "analytical": {"creativity": 0.2, "logic": 0.7, "cost": 0.1},
                "balanced": {"creativity": 0.4, "logic": 0.4, "cost": 0.2}
            }
            
            weights = task_weights.get(task_type, task_weights["balanced"])
            
            best_model = None
            best_score = -1
            
            for model, capabilities in self.model_capabilities.items():
                # 计算综合评分
                score = (
                    capabilities["creativity"] * weights["creativity"] +
                    capabilities["logic"] * weights["logic"] +
                    capabilities["cost"] * weights["cost"]
                )
                
                # 预算限制检查
                if budget_limit and capabilities["cost"] < budget_limit:
                    continue
                
                if score > best_score:
                    best_score = score
                    best_model = model
            
            return best_model
    ```
    
    ### Step 4: 缓存和优化系统
    ```mermaid
    graph TD
        A[请求缓存] --> B[结果缓存]
        B --> C[模板缓存]
        C --> D[智能预加载]
        
        A --> A1[相似请求检测]
        B --> B1[LRU淘汰策略]
        C --> C1[热门模板预缓存]
        D --> D1[用户行为预测]
    ```
    
    **缓存系统实现**：
    ```python
    import hashlib
    import json
    from typing import Optional
    
    class AICache:
        def __init__(self, max_size: int = 1000):
            self.cache = {}
            self.max_size = max_size
            self.access_order = []
        
        def _generate_key(self, prompt: str, model: str, **kwargs) -> str:
            """生成缓存键"""
            cache_data = {
                "prompt": prompt,
                "model": model,
                **kwargs
            }
            return hashlib.md5(
                json.dumps(cache_data, sort_keys=True).encode()
            ).hexdigest()
        
        def get(self, prompt: str, model: str, **kwargs) -> Optional[Dict]:
            """获取缓存结果"""
            key = self._generate_key(prompt, model, **kwargs)
            
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key]
            
            return None
        
        def set(self, prompt: str, model: str, result: Dict, **kwargs):
            """设置缓存结果"""
            key = self._generate_key(prompt, model, **kwargs)
            
            # LRU淘汰
            if len(self.cache) >= self.max_size:
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[key] = result
            self.access_order.append(key)
    ```
    
    ### Step 5: 监控和告警系统
    ```mermaid
    flowchart LR
        A[性能监控] --> D[监控面板]
        B[成本监控] --> D
        C[健康检查] --> D
        
        D --> E[告警系统]
        E --> F[自动处理]
        E --> G[人工介入]
    ```
    
    **监控指标收集**：
    ```python
    class AIMonitor:
        def __init__(self):
            self.metrics = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "total_cost": 0.0,
                "average_response_time": 0.0,
                "model_usage": {}
            }
        
        def record_request(
            self, 
            model: str, 
            success: bool, 
            response_time: float,
            cost: float = 0.0
        ):
            """记录请求指标"""
            self.metrics["total_requests"] += 1
            
            if success:
                self.metrics["successful_requests"] += 1
            else:
                self.metrics["failed_requests"] += 1
            
            self.metrics["total_cost"] += cost
            
            # 更新平均响应时间
            total_time = (
                self.metrics["average_response_time"] * 
                (self.metrics["total_requests"] - 1) + 
                response_time
            )
            self.metrics["average_response_time"] = (
                total_time / self.metrics["total_requests"]
            )
            
            # 记录模型使用情况
            if model not in self.metrics["model_usage"]:
                self.metrics["model_usage"][model] = 0
            self.metrics["model_usage"][model] += 1
        
        def get_success_rate(self) -> float:
            """获取成功率"""
            if self.metrics["total_requests"] == 0:
                return 0.0
            
            return (
                self.metrics["successful_requests"] / 
                self.metrics["total_requests"]
            )
    ```
  </process>

  <criteria>
    ## AI集成质量评价标准
    
    ### 功能完整性
    - ✅ 支持所有主流AI模型
    - ✅ 统一接口调用正常
    - ✅ 智能路由工作正确
    - ✅ 缓存机制有效
    
    ### 性能指标
    - ✅ API调用成功率 > 95%
    - ✅ 平均响应时间 < 30秒
    - ✅ 缓存命中率 > 30%
    - ✅ 系统可用性 > 99%
    
    ### 安全性
    - ✅ API密钥加密存储
    - ✅ 访问权限控制完整
    - ✅ 审计日志记录完整
    - ✅ 数据隐私保护到位
    
    ### 可维护性
    - ✅ 代码结构清晰
    - ✅ 文档完整详细
    - ✅ 监控告警完善
    - ✅ 扩展性良好
  </criteria>
</execution>
