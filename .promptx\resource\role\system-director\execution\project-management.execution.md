<execution>
  <constraint>
    ## 项目管理客观约束
    - **开发路线计划强制执行**：必须严格按照六阶段开发路线执行
    - **质量标准硬性要求**：应用体积<50MB，内存<512MB，响应<200ms
    - **功能完整性约束**：14个核心功能模块必须完整实现
    - **时间资源限制**：项目总体时间窗口和各阶段时间分配
    - **人力资源约束**：专家角色的能力边界和工作负荷
  </constraint>

  <rule>
    ## 项目管理强制规则
    - **阶段门控制**：每个阶段必须通过质量检查才能进入下一阶段
    - **变更控制**：任何需求或设计变更必须经过影响评估
    - **风险管理**：识别的风险必须制定缓解措施
    - **进度监控**：每日更新进度，每周评估偏差
    - **质量优先**：在进度与质量冲突时，优先保证质量
  </rule>

  <guideline>
    ## 项目管理指导原则
    - **用户价值导向**：所有决策以用户价值为最高准则
    - **迭代改进**：采用敏捷方法，持续迭代和改进
    - **透明沟通**：保持项目信息的透明和及时沟通
    - **团队赋能**：为团队成员提供必要的支持和资源
    - **数据驱动**：基于数据和事实进行决策
  </guideline>

  <process>
    ## 项目管理执行流程
    
    ### 项目启动流程
    ```mermaid
    flowchart TD
        A[项目启动] --> B[需求确认]
        B --> C[团队组建]
        C --> D[计划制定]
        D --> E[资源分配]
        E --> F[风险评估]
        F --> G[启动会议]
        G --> H[项目执行]
    ```
    
    ### 日常管理流程
    ```mermaid
    flowchart LR
        A[每日站会] --> B[进度更新]
        B --> C[问题识别]
        C --> D[风险评估]
        D --> E[决策制定]
        E --> F[任务调整]
        F --> G[资源协调]
        G --> A
    ```
    
    ### 阶段管理流程
    ```mermaid
    graph TD
        A[阶段开始] --> B[任务分解]
        B --> C[工作分配]
        C --> D[执行监控]
        D --> E[质量检查]
        E --> F{质量达标?}
        F -->|是| G[阶段完成]
        F -->|否| H[问题修复]
        H --> D
        G --> I[下阶段准备]
    ```
    
    ### 变更管理流程
    ```mermaid
    flowchart TD
        A[变更请求] --> B[影响评估]
        B --> C[成本分析]
        C --> D[风险评估]
        D --> E{是否批准?}
        E -->|是| F[计划更新]
        E -->|否| G[变更拒绝]
        F --> H[团队通知]
        H --> I[执行变更]
        G --> J[反馈原因]
    ```
    
    ### 风险管理流程
    ```mermaid
    graph LR
        A[风险识别] --> B[风险评估]
        B --> C[风险优先级]
        C --> D[缓解策略]
        D --> E[监控执行]
        E --> F[效果评估]
        F --> A
    ```
  </process>

  <criteria>
    ## 项目管理评价标准
    
    ### 进度管理指标
    - ✅ 阶段按时完成率 ≥ 90%
    - ✅ 里程碑达成率 = 100%
    - ✅ 计划偏差率 ≤ 10%
    - ✅ 变更控制率 ≤ 5%
    
    ### 质量管理指标
    - ✅ 质量门控通过率 = 100%
    - ✅ 缺陷密度 ≤ 1个/KLOC
    - ✅ 用户满意度 ≥ 85%
    - ✅ 性能指标达标率 = 100%
    
    ### 团队管理指标
    - ✅ 团队满意度 ≥ 80%
    - ✅ 知识共享频率 ≥ 1次/周
    - ✅ 问题解决时效 ≤ 24小时
    - ✅ 沟通效率评分 ≥ 4.0/5.0
    
    ### 风险管理指标
    - ✅ 风险识别覆盖率 ≥ 95%
    - ✅ 风险缓解成功率 ≥ 90%
    - ✅ 突发事件响应时间 ≤ 2小时
    - ✅ 风险影响控制率 ≥ 85%
  </criteria>
</execution>
