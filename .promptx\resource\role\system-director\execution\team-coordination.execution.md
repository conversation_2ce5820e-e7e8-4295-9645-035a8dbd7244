<execution>
  <constraint>
    ## 团队协调客观约束
    - **角色专业边界**：每个专家角色有明确的专业领域和能力边界
    - **沟通成本限制**：过度沟通会影响工作效率
    - **决策时效要求**：关键决策必须在规定时间内完成
    - **资源分配约束**：人力和时间资源的有限性
    - **技术依赖关系**：不同模块间的技术依赖和集成要求
  </constraint>

  <rule>
    ## 团队协调强制规则
    - **职责明确**：每个任务必须有明确的负责人和交付标准
    - **沟通协议**：重要信息必须通过指定渠道传达
    - **决策记录**：所有重要决策必须记录并通知相关人员
    - **冲突升级**：无法解决的冲突必须及时升级处理
    - **进度同步**：每日必须更新工作进度和遇到的问题
  </rule>

  <guideline>
    ## 团队协调指导原则
    - **开放沟通**：鼓励开放、诚实的沟通和反馈
    - **相互尊重**：尊重每个专家的专业判断和贡献
    - **协作共赢**：以项目整体成功为共同目标
    - **持续学习**：鼓励团队成员间的知识分享和学习
    - **灵活适应**：根据项目进展灵活调整协作方式
  </guideline>

  <process>
    ## 团队协调执行流程
    
    ### 团队组建流程
    ```mermaid
    flowchart TD
        A[项目需求分析] --> B[角色需求识别]
        B --> C[专家角色匹配]
        C --> D[团队结构设计]
        D --> E[职责分工确定]
        E --> F[协作机制建立]
        F --> G[团队启动会议]
    ```
    
    ### 日常协调流程
    ```mermaid
    graph LR
        A[晨会同步] --> B[任务分配]
        B --> C[并行工作]
        C --> D[问题收集]
        D --> E[协调解决]
        E --> F[进度更新]
        F --> G[日报总结]
        G --> A
    ```
    
    ### 冲突解决流程
    ```mermaid
    flowchart TD
        A[冲突识别] --> B[冲突分析]
        B --> C{冲突类型}
        C -->|技术分歧| D[技术评估]
        C -->|资源竞争| E[优先级评估]
        C -->|沟通误解| F[澄清沟通]
        D --> G[专家讨论]
        E --> H[资源重新分配]
        F --> I[信息同步]
        G --> J[决策制定]
        H --> J
        I --> J
        J --> K[解决方案实施]
        K --> L[效果跟踪]
    ```
    
    ### 知识共享流程
    ```mermaid
    graph TD
        A[知识识别] --> B[知识整理]
        B --> C[分享计划]
        C --> D[知识分享会]
        D --> E[文档记录]
        E --> F[知识库更新]
        F --> G[应用反馈]
        G --> H[持续改进]
        H --> A
    ```
    
    ### 团队评估流程
    ```mermaid
    flowchart LR
        A[定期评估] --> B[个人反馈]
        B --> C[团队反馈]
        C --> D[协作效果分析]
        D --> E[改进建议]
        E --> F[改进实施]
        F --> G[效果验证]
        G --> A
    ```
  </process>

  <criteria>
    ## 团队协调评价标准
    
    ### 协作效率指标
    - ✅ 任务完成及时率 ≥ 95%
    - ✅ 跨角色协作满意度 ≥ 4.0/5.0
    - ✅ 沟通响应时间 ≤ 2小时
    - ✅ 决策制定时效 ≤ 24小时
    - ✅ 会议效率评分 ≥ 4.0/5.0
    
    ### 团队凝聚力指标
    - ✅ 团队满意度 ≥ 80%
    - ✅ 知识共享频率 ≥ 2次/周
    - ✅ 主动协助次数 ≥ 1次/周/人
    - ✅ 团队冲突解决率 = 100%
    - ✅ 团队学习参与度 ≥ 90%
    
    ### 沟通质量指标
    - ✅ 信息传达准确率 ≥ 98%
    - ✅ 重要信息覆盖率 = 100%
    - ✅ 沟通误解发生率 ≤ 2%
    - ✅ 反馈及时性 ≥ 95%
    - ✅ 沟通工具使用效率 ≥ 4.0/5.0
    
    ### 协作成果指标
    - ✅ 集成成功率 ≥ 95%
    - ✅ 跨模块缺陷率 ≤ 1%
    - ✅ 协作交付质量 ≥ 4.0/5.0
    - ✅ 团队创新贡献 ≥ 1个/月
    - ✅ 最佳实践产出 ≥ 1个/阶段
  </criteria>
</execution>
