<execution>
  <constraint>
    ## 设计技术约束
    - **性能要求**：Glassmorphism效果不得影响应用流畅性
    - **兼容性要求**：支持主流浏览器和操作系统的一致渲染
    - **可访问性标准**：符合WCAG 2.1 AA级无障碍设计标准
    - **响应式要求**：支持1024px-4K分辨率的完美适配
    - **加载性能**：首屏渲染时间不超过2秒
    - **内存占用**：CSS和图片资源总大小不超过5MB
  </constraint>

  <rule>
    ## 设计规范强制规则
    - **色彩一致性**：必须使用统一的色彩变量系统
    - **间距标准化**：所有间距必须基于8px倍数系统
    - **字体层次**：严格遵循定义的字体大小和行高规范
    - **组件复用**：禁止重复设计相似功能的组件
    - **状态完整性**：每个交互元素必须定义所有状态样式
    - **命名规范**：CSS类名必须遵循BEM命名约定
  </rule>

  <guideline>
    ## 设计指导原则
    - **用户优先**：所有设计决策以用户体验为最高优先级
    - **渐进增强**：基础功能优先，视觉效果作为增强
    - **性能意识**：在设计过程中持续考虑性能影响
    - **可维护性**：建立可扩展和易维护的设计系统
    - **测试驱动**：重要界面必须进行用户测试验证
    - **文档完善**：设计决策和规范必须有完整文档
  </guideline>

  <process>
    ## 完整设计流程
    
    ### Step 1: 需求分析与用户研究
    ```mermaid
    flowchart TD
        A[项目需求分析] --> B[用户画像研究]
        B --> C[使用场景分析]
        C --> D[竞品分析]
        D --> E[设计目标确定]
    ```
    
    **具体操作**：
    1. 分析AI小说助手的核心功能需求
    2. 研究网络小说创作者的工作习惯
    3. 分析长时间创作的界面需求
    4. 调研现有创作工具的设计优缺点
    5. 确定Glassmorphism设计的适用性
    
    ### Step 2: 设计系统构建
    ```mermaid
    graph LR
        A[色彩系统] --> B[字体系统]
        B --> C[间距系统]
        C --> D[组件库]
        D --> E[图标系统]
        E --> F[动效规范]
    ```
    
    **设计系统要素**：
    
    #### 色彩系统设计
    ```css
    /* 主题色彩变量 */
    :root {
      /* 主色调 - 专业蓝 */
      --primary-50: #eff6ff;
      --primary-500: #3b82f6;
      --primary-900: #1e3a8a;
      
      /* 次色调 - 活力绿 */
      --secondary-50: #ecfdf5;
      --secondary-500: #10b981;
      --secondary-900: #064e3b;
      
      /* 强调色 - 温暖橙 */
      --accent-50: #fffbeb;
      --accent-500: #f59e0b;
      --accent-900: #78350f;
      
      /* Glassmorphism专用 */
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    ```
    
    #### 毛玻璃效果规范
    ```css
    .glass-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 12px;
      box-shadow: var(--glass-shadow);
    }
    
    .glass-button {
      background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.1), 
        rgba(59, 130, 246, 0.2));
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
    }
    ```
    
    ### Step 3: 界面布局设计
    ```mermaid
    flowchart TD
        A[信息架构设计] --> B[线框图绘制]
        B --> C[视觉设计]
        C --> D[交互设计]
        D --> E[原型制作]
    ```
    
    **布局设计原则**：
    
    #### 主界面布局结构
    ```
    ┌─────────────────────────────────────────────────────────┐
    │                    应用标题栏 (64px)                     │
    ├─────────────────────────────────────────────────────────┤
    │                    顶部工具栏 (48px)                     │
    ├──────────────────┬──────────────────────────────────────┤
    │                  │                                      │
    │   左侧功能导航    │            主要内容区域               │
    │     (280px)      │              (flex)                 │
    │                  │                                      │
    │  [毛玻璃卡片]     │         [毛玻璃内容面板]              │
    │                  │                                      │
    └──────────────────┴──────────────────────────────────────┘
    ```
    
    ### Step 4: 组件设计与开发
    ```mermaid
    graph TD
        A[基础组件] --> A1[Button]
        A --> A2[Input]
        A --> A3[Card]
        A --> A4[Modal]
        
        B[复合组件] --> B1[Form]
        B --> B2[Table]
        B --> B3[Navigation]
        B --> B4[Sidebar]
        
        C[专用组件] --> C1[Editor]
        C --> C2[OutlineTree]
        C --> C3[CharacterCard]
        C --> C4[ProgressBar]
    ```
    
    **组件设计规范**：
    
    #### 按钮组件设计
    ```vue
    <template>
      <button 
        :class="buttonClasses"
        @click="handleClick"
      >
        <slot />
      </button>
    </template>
    
    <style scoped>
    .glass-button {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }
    
    .glass-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    }
    </style>
    ```
    
    ### Step 5: 响应式适配
    ```mermaid
    flowchart LR
        A[桌面端 1200px+] --> B[平板端 768-1199px]
        B --> C[小屏 <768px]
        
        A --> A1[三栏布局]
        B --> B1[两栏布局]
        C --> C1[单栏布局]
    ```
    
    **响应式断点设计**：
    ```css
    /* 响应式断点 */
    @media (max-width: 768px) {
      .layout-container {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        height: auto;
      }
    }
    
    @media (min-width: 1200px) {
      .content-area {
        max-width: 1200px;
        margin: 0 auto;
      }
    }
    ```
    
    ### Step 6: 交互动效设计
    ```mermaid
    graph LR
        A[页面转场] --> B[组件动画]
        B --> C[加载状态]
        C --> D[反馈动效]
        
        A --> A1[淡入淡出]
        B --> B1[悬停效果]
        C --> C1[骨架屏]
        D --> D1[成功提示]
    ```
    
    **动效设计原则**：
    - 持续时间：0.2-0.5秒
    - 缓动函数：ease-out
    - 性能优化：使用transform和opacity
    - 可访问性：支持减少动画偏好设置
  </process>

  <criteria>
    ## 设计质量评价标准
    
    ### 视觉质量标准
    - ✅ Glassmorphism效果自然美观
    - ✅ 色彩搭配和谐统一
    - ✅ 字体层次清晰易读
    - ✅ 间距比例协调合理
    
    ### 用户体验标准
    - ✅ 界面操作直观易懂
    - ✅ 信息层次清晰明确
    - ✅ 交互反馈及时准确
    - ✅ 学习成本低
    
    ### 技术实现标准
    - ✅ 渲染性能流畅
    - ✅ 跨平台兼容性好
    - ✅ 响应式适配完整
    - ✅ 代码质量高可维护
    
    ### 可访问性标准
    - ✅ 色彩对比度符合标准
    - ✅ 键盘导航完整
    - ✅ 屏幕阅读器友好
    - ✅ 支持个性化设置
  </criteria>
</execution>
